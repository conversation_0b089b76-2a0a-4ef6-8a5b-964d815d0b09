from flask import Blueprint, request, jsonify
from utils.length_measurement_utils import generate_changduceliang_plot, calculate_uncertainty_components
from utils.common_utils import model_registry
import traceback
import math

length_measurement_bp = Blueprint('length_measurement', __name__, url_prefix='/length_measurement')

@length_measurement_bp.route('/plot', methods=['POST'])
def generate_plot():
    """生成长度测量实验图表"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        plot_data = generate_changduceliang_plot(data)
        return jsonify({'plot_url': plot_data})

    except Exception as e:
        print(f"生成图表错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'生成图表时发生错误: {str(e)}'}), 500

@length_measurement_bp.route('/calculate_uncertainty', methods=['POST'])
def calculate_uncertainty():
    """计算不确定度"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        measurements = data.get('measurements', [])
        instrument_error = data.get('instrument_error', 0.02)

        if not measurements:
            return jsonify({'error': '测量数据为空'}), 400

        mean_value, ua, ub, uc = calculate_uncertainty_components(measurements, instrument_error)

        return jsonify({
            'mean': round(mean_value, 4),
            'ua': round(ua, 4),
            'ub': round(ub, 4),
            'uc': round(uc, 4),
            'n': len(measurements),
            'std_dev': round(math.sqrt(len(measurements)) * ua, 4) if ua > 0 else 0
        })

    except Exception as e:
        print(f"计算不确定度错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'计算不确定度时发生错误: {str(e)}'}), 500

@length_measurement_bp.route('/check_calculation', methods=['POST'])
def check_calculation():
    """AI检查学生的不确定度计算结果"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        model_id = data.get('model_id', '')
        student_result = data.get('student_result', {})
        correct_result = data.get('correct_result', {})
        calculation_type = data.get('calculation_type', 'uncertainty')

        # 创建检查提示词
        check_prompt = create_calculation_check_prompt(student_result, correct_result, calculation_type)

        # 调用AI检查
        try:
            check_result = model_registry.get_analysis(check_prompt, model_id)
            return jsonify({'check_result': check_result})
        except Exception as ai_error:
            print(f"AI检查错误: {ai_error}")
            return jsonify({'error': f'AI检查失败: {str(ai_error)}'}), 500

    except Exception as e:
        print(f"检查计算错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'检查计算时发生错误: {str(e)}'}), 500

@length_measurement_bp.route('/analyze', methods=['POST'])
def analyze_data():
    """AI分析长度测量实验数据"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        # 获取模型ID
        model_id = data.get('model_id', '')

        # 准备分析提示词
        analysis_prompt = create_analysis_prompt(data)

        # 调用AI分析
        try:
            analysis_result = model_registry.get_analysis(analysis_prompt, model_id)
            return jsonify({'analysis': analysis_result})
        except Exception as ai_error:
            print(f"AI分析错误: {ai_error}")
            return jsonify({'error': f'AI分析失败: {str(ai_error)}'}), 500

    except Exception as e:
        print(f"数据分析错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'数据分析时发生错误: {str(e)}'}), 500

def create_calculation_check_prompt(student_result, correct_result, calculation_type):
    """创建用于AI检查计算的提示词"""
    prompt = f"""# 物理实验计算检查

## 计算类型
{calculation_type}

## 学生计算结果
"""

    for key, value in student_result.items():
        prompt += f"- {key}: {value}\n"

    prompt += "\n## 正确答案\n"
    for key, value in correct_result.items():
        prompt += f"- {key}: {value}\n"

    prompt += """
## 检查要求

请作为一位物理实验教师，检查学生的计算结果：

1. **正确性判断**：学生的计算是否正确？
2. **误差分析**：如果有误差，误差有多大？是否在可接受范围内？
3. **计算过程**：推测学生可能的计算过程和思路
4. **改进建议**：如果有错误，给出具体的改进建议
5. **鼓励评价**：给出积极的反馈和鼓励

请用温和、鼓励的语气，既要指出问题，也要肯定学生的努力。
"""

    return prompt

def create_analysis_prompt(data):
    """创建用于AI分析的提示词"""
    prompt = """# 长度测量实验数据分析

## 实验背景
这是一个长度测量实验，重点学习不确定度的计算方法：
- 游标卡尺（精度0.02mm）测量圆管
- 螺旋测微计（精度0.004mm）测量小球
- 数字式测量工具测量铜棒

## 实验数据

"""

    # 添加各种测量数据
    if 'vernier_data' in data:
        vernier_data = data['vernier_data']
        prompt += "### 游标卡尺测量数据\n"
        if vernier_data.get('outer_diameter'):
            prompt += f"- 外径: {vernier_data['outer_diameter']} mm\n"
        if vernier_data.get('inner_diameter'):
            prompt += f"- 内径: {vernier_data['inner_diameter']} mm\n"
        if vernier_data.get('height'):
            prompt += f"- 高度: {vernier_data['height']} mm\n"
        prompt += "\n"

    if 'micrometer_data' in data:
        micrometer_data = data['micrometer_data']
        prompt += "### 螺旋测微计测量数据\n"
        if micrometer_data.get('diameter'):
            prompt += f"- 小球直径: {micrometer_data['diameter']} mm\n"
        prompt += "\n"

    if 'digital_data' in data:
        digital_data = data['digital_data']
        prompt += "### 数字式测量工具数据\n"
        if digital_data.get('length'):
            prompt += f"- 铜棒长度: {digital_data['length']} mm\n"
        if digital_data.get('diameter'):
            prompt += f"- 铜棒直径: {digital_data['diameter']} mm\n"
        prompt += "\n"

    # 添加不确定度分析结果
    if 'uncertainty_analysis' in data:
        prompt += "### 不确定度分析结果\n"
        uncertainty_data = data['uncertainty_analysis']
        for instrument, values in uncertainty_data.items():
            prompt += f"**{instrument}**:\n"
            prompt += f"- A类不确定度: {values.get('ua', 'N/A')} mm\n"
            prompt += f"- B类不确定度: {values.get('ub', 'N/A')} mm\n"
            prompt += f"- C类不确定度: {values.get('uc', 'N/A')} mm\n"
        prompt += "\n"

    prompt += """
## 分析要求

请对以上实验数据进行详细分析，包括：

1. **数据质量评估**
   - 各组测量数据的稳定性和重现性
   - 异常数据点的识别和分析

2. **不确定度分析**
   - A类、B类、C类不确定度的计算是否合理
   - 不同仪器不确定度的比较
   - 不确定度对最终结果的影响

3. **测量方法评价**
   - 不同测量工具的适用性
   - 测量精度的比较
   - 操作规范性的评估

4. **结果表示**
   - 测量结果的正确表示方法
   - 有效数字的处理
   - 不确定度的合理表达

5. **实验改进建议**
   - 减小不确定度的方法
   - 提高测量精度的措施
   - 实验操作的改进建议

请用专业的物理实验语言进行分析，并给出具体的数值计算和结论。
"""

    return prompt
