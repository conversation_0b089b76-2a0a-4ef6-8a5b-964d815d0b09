from flask import Blueprint, request, jsonify
import traceback
from utils.micro_length_lever_utils import generate_micro_length_lever_plot, analyze_micro_length_lever_data_with_ai

micro_length_lever_bp = Blueprint('micro_length_lever', __name__, url_prefix='/micro_length_lever')

@micro_length_lever_bp.route('/plot', methods=['POST'])
def micro_length_lever_plot_route():
    try:
        data = request.get_json()
        if not data or 'n_list' not in data or 's_list' not in data or 'L1' not in data or 'L2' not in data or 'D' not in data:
            return jsonify({'error': '缺少绘图所需数据。'}), 400
        n_list = [float(x) for x in data['n_list']]
        s_list = [float(x) for x in data['s_list']]
        L1 = float(data['L1'])
        L2 = float(data['L2'])
        D = float(data['D'])
        plot_url = generate_micro_length_lever_plot(n_list, s_list, L1, L2, D)
        return jsonify({'plot_url': plot_url})
    except Exception as e:
        traceback.print_exc()
        return jsonify({'error': f'生成图形时发生错误: {str(e)}'}), 500

@micro_length_lever_bp.route('/analyze', methods=['POST'])
def micro_length_lever_analyze_route():
    try:
        data = request.get_json()
        if not data or 'n_list' not in data or 's_list' not in data or 'L1' not in data or 'L2' not in data or 'D' not in data:
            return jsonify({'error': '缺少分析所需数据。'}), 400
        n_list = [float(x) for x in data['n_list']]
        s_list = [float(x) for x in data['s_list']]
        L1 = float(data['L1'])
        L2 = float(data['L2'])
        D = float(data['D'])
        model_id = data.get('model_id')
        analysis_result = analyze_micro_length_lever_data_with_ai(n_list, s_list, L1, L2, D, model_id)
        return analysis_result, 200, {'Content-Type': 'text/plain; charset=utf-8'}
    except Exception as e:
        traceback.print_exc()
        return f'分析过程中发生错误: {str(e)}', 500, {'Content-Type': 'text/plain; charset=utf-8'} 