from flask import Blueprint, render_template, request, jsonify
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')
import numpy as np
import io
import base64
from datetime import datetime
import json
from utils.common_utils import model_registry, experiment_db, generate_experiment_plot_base

# 创建蓝图
gravity_measurement_photogate_bp = Blueprint('gravity_measurement_photogate', __name__)

@gravity_measurement_photogate_bp.route('/gravity_measurement_photogate')
def gravity_measurement_photogate_experiment():
    """光电控制法测量重力加速度实验页面"""
    available_models = model_registry.get_app_models()
    return render_template('experiments/gravity_measurement_photogate.html', available_models=available_models)

@gravity_measurement_photogate_bp.route('/gravity_measurement_photogate/plot', methods=['POST'])
def generate_free_fall_plot():
    """生成自由落体实验的图形"""
    try:
        data = request.get_json()
        heights = data.get('heights', [])
        times = data.get('times', [])
        gravity_values = data.get('gravity_values', [])

        if not heights or not times:
            return jsonify({'error': '数据不足，无法生成图形'})

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

        # 第一个子图：高度与时间的关系
        ax1.scatter(times, heights, color='blue', s=100, alpha=0.7, label='实验数据')
        t_theory = np.linspace(0, max(times) * 1.1, 100)
        h_theory = 0.5 * 9.8 * t_theory**2
        ax1.plot(t_theory, h_theory, 'r--', linewidth=2, label='理论曲线 (g=9.8 m/s²)')
        ax1.set_xlabel('时间 t (s)', fontsize=12)
        ax1.set_ylabel('高度 h (m)', fontsize=12)
        ax1.set_title('自由落体运动：高度-时间关系', fontsize=14, fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.set_xlim(0, max(times) * 1.1)
        ax1.set_ylim(0, max(heights) * 1.1)

        # 第二个子图：重力加速度分布
        ax2.bar(range(1, len(gravity_values) + 1), gravity_values, 
                color='green', alpha=0.7, label='测量值')
        ax2.axhline(y=9.8, color='red', linestyle='--', linewidth=2, label='标准值 (9.8 m/s²)')
        avg_g = np.mean(gravity_values)
        ax2.axhline(y=avg_g, color='blue', linestyle='-', linewidth=2, label=f'平均值 ({avg_g:.2f} m/s²)')
        ax2.set_xlabel('测量次数', fontsize=12)
        ax2.set_ylabel('重力加速度 g (m/s²)', fontsize=12)
        ax2.set_title('重力加速度测量结果', fontsize=14, fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.set_xticks(range(1, len(gravity_values) + 1))
        std_g = np.std(gravity_values)
        relative_error = abs(avg_g - 9.8) / 9.8 * 100
        fig.suptitle(f'自由落体实验数据分析\n平均重力加速度: {avg_g:.2f} ± {std_g:.2f} m/s², 相对误差: {relative_error:.1f}%', 
                    fontsize=16, fontweight='bold')
        plt.tight_layout()
        # 保存图片到base64
        img_base64 = generate_experiment_plot_base(fig, dpi=300)
        return jsonify({'plot_url': img_base64})
    except Exception as e:
        return jsonify({'error': f'生成图形时发生错误: {str(e)}'})

@gravity_measurement_photogate_bp.route('/gravity_measurement_photogate/analyze', methods=['POST'])
def analyze_free_fall_data():
    """AI分析自由落体实验数据"""
    try:
        data = request.get_json()
        heights = data.get('heights', [])
        times = data.get('times', [])
        gravity_values = data.get('gravity_values', [])
        model_id = data.get('model_id', 'default')

        if not heights or not times or not gravity_values:
            return jsonify({'error': '数据不足，无法进行分析'})

        # 计算统计量
        avg_g = np.mean(gravity_values)
        std_g = np.std(gravity_values)
        relative_error = abs(avg_g - 9.8) / 9.8 * 100

        # 构建分析提示
        analysis_prompt = f"""
请对以下自由落体实验数据进行专业分析：

实验数据：
- 测量次数：{len(heights)}次
- 高度范围：{min(heights):.2f} - {max(heights):.2f} m
- 时间范围：{min(times):.3f} - {max(times):.3f} s
- 重力加速度测量值：{', '.join([f'{g:.2f}' for g in gravity_values])} m/s²

计算结果：
- 平均重力加速度：{avg_g:.2f} m/s²
- 标准偏差：{std_g:.2f} m/s²
- 相对误差：{relative_error:.1f}%

请从以下几个方面进行分析：
1. 实验完成度评估
2. 数据质量分析
3. 误差来源分析
4. 与理论值的比较
5. 改进建议
6. 实验评价

请用中文回答，格式要清晰易读。
"""

        # 调用AI分析
        analysis_result = model_registry.get_analysis(analysis_prompt, model_id)
        
        return jsonify({'analysis': analysis_result})

    except Exception as e:
        return jsonify({'error': f'分析数据时发生错误: {str(e)}'})

@gravity_measurement_photogate_bp.route('/gravity_measurement_photogate/submit', methods=['POST'])
def submit_free_fall_experiment():
    """提交自由落体实验数据"""
    try:
        data = request.get_json()
        
        # 验证必要字段
        required_fields = ['student_id', 'student_name', 'heights', 'times', 'gravity_values']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'})

        # 准备保存的数据
        experiment_data = {
            'experiment_type': 'free_fall',
            'student_id': data['student_id'],
            'student_name': data['student_name'],
            'heights': data['heights'],
            'times': data['times'],
            'gravity_values': data['gravity_values'],
            'average_gravity': np.mean(data['gravity_values']),
            'std_gravity': np.std(data['gravity_values']),
            'relative_error': abs(np.mean(data['gravity_values']) - 9.8) / 9.8 * 100,
            'plot_data': data.get('plot_data'),
            'analysis_result': data.get('analysis_result'),
            'submission_time': datetime.now().isoformat()
        }

        # 保存数据到数据库
        connection = experiment_db.get_connection()
        if not connection:
            return jsonify({'error': '无法连接到数据库'})

        try:
            with connection.cursor() as cursor:
                # 检查学生是否存在
                sql_check_student = "SELECT id FROM students WHERE id = %s"
                cursor.execute(sql_check_student, (data['student_id'],))
                student_record = cursor.fetchone()
                if not student_record:
                    return jsonify({'error': f'学生ID {data["student_id"]} 不存在于学生信息库中'})

                # 计算新版本号
                sql_max_version = "SELECT MAX(version) as max_version FROM experiments WHERE student_id = %s AND experiment_type = %s"
                cursor.execute(sql_max_version, (data['student_id'], 'free_fall'))
                result = cursor.fetchone()
                current_version = result['max_version'] if result and result['max_version'] is not None else 0
                new_version = current_version + 1

                # 插入实验数据
                sql_insert = """
                INSERT INTO experiments
                (student_id, experiment_type, experiment_data, version, submit_time, status)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                current_db_time = datetime.now()

                cursor.execute(sql_insert, (
                    data['student_id'],
                    'free_fall',
                    json.dumps(experiment_data, ensure_ascii=False),
                    new_version,
                    current_db_time,
                    'submitted'
                ))
                last_id = cursor.lastrowid
            connection.commit()
            
            return jsonify({
                'success': True,
                'record_id': last_id,
                'version': new_version,
                'message': '实验数据提交成功'
            })

        except Exception as e:
            connection.rollback()
            return jsonify({'error': f'保存数据时发生错误: {str(e)}'})
        finally:
            connection.close()

    except Exception as e:
        return jsonify({'error': f'提交数据时发生错误: {str(e)}'}) 