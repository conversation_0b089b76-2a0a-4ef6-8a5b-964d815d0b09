from flask import Blueprint, render_template, request, jsonify
from utils.lens_focal_length_utils import (
    generate_lens_focal_length_plot, 
    calculate_focal_length_uncertainty,
    analyze_lens_focal_length_data_with_ai
)
from utils.common_utils import model_registry, experiment_db
import traceback
import math
from datetime import datetime
import json

lens_focal_length_bp = Blueprint('lens_focal_length', __name__, url_prefix='/lens_focal_length')

@lens_focal_length_bp.route('/lens_focal_length')
def lens_focal_length_experiment():
    """薄透镜焦距测定实验页面"""
    available_models = model_registry.get_app_models()
    return render_template('experiments/lens_focal_length.html', available_models=available_models)

@lens_focal_length_bp.route('/plot', methods=['POST'])
def generate_plot():
    """生成薄透镜焦距测定实验图表"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        plot_data = generate_lens_focal_length_plot(data)
        return jsonify({'plot_url': plot_data})

    except Exception as e:
        print(f"生成图表错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'生成图表时发生错误: {str(e)}'}), 500

@lens_focal_length_bp.route('/calculate_uncertainty', methods=['POST'])
def calculate_uncertainty():
    """计算焦距测量的不确定度"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        measurements = data.get('measurements', [])
        method_type = data.get('method_type', 'uv')  # uv, autocollimation, conjugate
        instrument_error = data.get('instrument_error', 0.1)  # 默认0.1cm

        if not measurements:
            return jsonify({'error': '测量数据为空'}), 400

        mean_value, ua, ub, uc = calculate_focal_length_uncertainty(measurements, instrument_error)

        return jsonify({
            'mean': round(mean_value, 3),
            'ua': round(ua, 4),
            'ub': round(ub, 4),
            'uc': round(uc, 4),
            'n': len(measurements),
            'std_dev': round(math.sqrt(len(measurements)) * ua, 4) if ua > 0 else 0,
            'method_type': method_type
        })

    except Exception as e:
        print(f"计算不确定度错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'计算不确定度时发生错误: {str(e)}'}), 500

@lens_focal_length_bp.route('/analyze', methods=['POST'])
def analyze_data():
    """AI分析薄透镜焦距测定实验数据"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        # 获取模型ID
        model_id = data.get('model_id', '')

        # 准备分析提示词
        analysis_prompt = create_analysis_prompt(data)

        # 调用AI分析
        try:
            analysis_result = model_registry.get_analysis(analysis_prompt, model_id)
            return jsonify({'analysis': analysis_result})
        except Exception as ai_error:
            print(f"AI分析错误: {ai_error}")
            return jsonify({'error': f'AI分析失败: {str(ai_error)}'}), 500

    except Exception as e:
        print(f"数据分析错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'数据分析时发生错误: {str(e)}'}), 500

@lens_focal_length_bp.route('/submit', methods=['POST'])
def submit_experiment():
    """提交薄透镜焦距测定实验数据"""
    try:
        data = request.get_json()
        
        # 验证必要字段
        required_fields = ['student_id', 'student_name']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'})

        # 准备保存的数据
        experiment_data = {
            'experiment_type': 'lens_focal_length',
            'student_id': data['student_id'],
            'student_name': data['student_name'],
            'convex_lens_data': data.get('convex_lens_data', {}),
            'concave_lens_data': data.get('concave_lens_data', {}),
            'analysis_result': data.get('analysis_result'),
            'plot_data': data.get('plot_data'),
            'submission_time': datetime.now().isoformat()
        }

        # 保存数据到数据库
        connection = experiment_db.get_connection()
        if not connection:
            return jsonify({'error': '无法连接到数据库'})

        try:
            with connection.cursor() as cursor:
                # 检查学生是否存在
                sql_check_student = "SELECT id FROM students WHERE id = %s"
                cursor.execute(sql_check_student, (data['student_id'],))
                student_record = cursor.fetchone()
                if not student_record:
                    return jsonify({'error': f'学生ID {data["student_id"]} 不存在于学生信息库中'})

                # 计算新版本号
                sql_max_version = "SELECT MAX(version) as max_version FROM experiments WHERE student_id = %s AND experiment_type = %s"
                cursor.execute(sql_max_version, (data['student_id'], 'lens_focal_length'))
                result = cursor.fetchone()
                current_version = result['max_version'] if result and result['max_version'] is not None else 0
                new_version = current_version + 1

                # 插入实验数据
                sql_insert = """
                INSERT INTO experiments
                (student_id, experiment_type, experiment_data, version, submit_time, status)
                VALUES (%s, %s, %s, %s, %s, %s)
                """
                current_db_time = datetime.now()

                cursor.execute(sql_insert, (
                    data['student_id'],
                    'lens_focal_length',
                    json.dumps(experiment_data, ensure_ascii=False),
                    new_version,
                    current_db_time,
                    'submitted'
                ))
                last_id = cursor.lastrowid
            connection.commit()
            
            return jsonify({
                'success': True,
                'record_id': last_id,
                'version': new_version,
                'message': '实验数据提交成功'
            })

        except Exception as e:
            connection.rollback()
            return jsonify({'error': f'保存数据时发生错误: {str(e)}'})
        finally:
            connection.close()

    except Exception as e:
        return jsonify({'error': f'提交数据时发生错误: {str(e)}'})

def create_analysis_prompt(data):
    """创建用于AI分析的提示词"""
    prompt = """# 薄透镜焦距测定实验数据分析

## 实验背景
这是一个薄透镜焦距测定实验，主要内容包括：
- 凸透镜焦距测量（物距像距法、自准法、共轭法）
- 凹透镜焦距测量（物距像距法、自准法）
- 不同测量方法的精度比较和误差分析

## 实验数据

"""

    # 添加凸透镜测量数据
    if 'convex_lens_data' in data:
        convex_data = data['convex_lens_data']
        prompt += "### 凸透镜测量数据\n"
        
        if convex_data.get('uv_method'):
            uv_data = convex_data['uv_method']
            prompt += "**物距像距法:**\n"
            prompt += f"- 平均焦距: {uv_data.get('mean_focal_length', 'N/A')} cm\n"
            prompt += f"- 标准差: {uv_data.get('std_dev', 'N/A')} cm\n"
        
        if convex_data.get('autocollimation_method'):
            auto_data = convex_data['autocollimation_method']
            prompt += "**自准法:**\n"
            prompt += f"- 平均焦距: {auto_data.get('mean_focal_length', 'N/A')} cm\n"
            prompt += f"- 标准差: {auto_data.get('std_dev', 'N/A')} cm\n"
        
        if convex_data.get('conjugate_method'):
            conj_data = convex_data['conjugate_method']
            prompt += "**共轭法:**\n"
            prompt += f"- 平均焦距: {conj_data.get('mean_focal_length', 'N/A')} cm\n"
            prompt += f"- 标准差: {conj_data.get('std_dev', 'N/A')} cm\n"

    # 添加凹透镜测量数据
    if 'concave_lens_data' in data:
        concave_data = data['concave_lens_data']
        prompt += "### 凹透镜测量数据\n"
        
        if concave_data.get('uv_method'):
            uv_data = concave_data['uv_method']
            prompt += "**物距像距法:**\n"
            prompt += f"- 平均焦距: {uv_data.get('mean_focal_length', 'N/A')} cm\n"
            prompt += f"- 标准差: {uv_data.get('std_dev', 'N/A')} cm\n"
        
        if concave_data.get('autocollimation_method'):
            auto_data = concave_data['autocollimation_method']
            prompt += "**自准法:**\n"
            prompt += f"- 平均焦距: {auto_data.get('mean_focal_length', 'N/A')} cm\n"
            prompt += f"- 标准差: {auto_data.get('std_dev', 'N/A')} cm\n"

    prompt += """
## 分析要求

请对以上薄透镜焦距测定实验数据进行详细分析，包括：

1. **测量方法比较**
   - 不同测量方法的优缺点
   - 各方法的适用条件和精度
   - 测量结果的可靠性评估

2. **数据质量分析**
   - 测量数据的稳定性和重现性
   - 异常数据点的识别和分析
   - 测量精度的评估

3. **误差分析**
   - 系统误差和随机误差的识别
   - 主要误差来源的分析
   - 不确定度的合理估算

4. **结果一致性**
   - 不同方法测量结果的一致性
   - 凸透镜和凹透镜测量结果的合理性
   - 与理论预期的比较

5. **实验改进建议**
   - 提高测量精度的方法
   - 减小误差的措施
   - 操作技巧的改进

请用专业的光学实验语言进行分析，并给出具体的数值计算和结论。
"""

    return prompt 