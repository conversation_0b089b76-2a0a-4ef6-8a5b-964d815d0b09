from flask import Blueprint, request, jsonify
from utils.spectrometer_utils import (
    generate_spectrometer_plot, 
    calculate_refractive_index,
    calculate_apex_angle,
    validate_angle_measurement
)
from utils.common_utils import model_registry
import traceback
import math

spectrometer_bp = Blueprint('spectrometer', __name__, url_prefix='/spectrometer')

@spectrometer_bp.route('/plot', methods=['POST'])
def generate_plot():
    """生成分光计实验图表"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        plot_data = generate_spectrometer_plot(data)
        return jsonify({'plot_url': plot_data})

    except Exception as e:
        print(f"生成图表错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'生成图表时发生错误: {str(e)}'}), 500

@spectrometer_bp.route('/calculate_refractive_index', methods=['POST'])
def calculate_n():
    """计算折射率"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        apex_angle = data.get('apex_angle')
        min_deviation = data.get('min_deviation')

        if apex_angle is None or min_deviation is None:
            return jsonify({'error': '缺少必要的角度数据'}), 400

        refractive_index = calculate_refractive_index(apex_angle, min_deviation)

        return jsonify({
            'refractive_index': round(refractive_index, 4),
            'apex_angle': apex_angle,
            'min_deviation': min_deviation
        })

    except Exception as e:
        print(f"计算折射率错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'计算折射率时发生错误: {str(e)}'}), 500

@spectrometer_bp.route('/calculate_apex_angle', methods=['POST'])
def calculate_apex():
    """计算三棱镜顶角"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        reflection_angles = data.get('reflection_angles', [])

        if len(reflection_angles) < 2:
            return jsonify({'error': '需要至少两个反射角度'}), 400

        apex_angle = calculate_apex_angle(reflection_angles[0], reflection_angles[1])

        return jsonify({
            'apex_angle': round(apex_angle, 4),
            'reflection_angle_1': reflection_angles[0],
            'reflection_angle_2': reflection_angles[1]
        })

    except Exception as e:
        print(f"计算顶角错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'计算顶角时发生错误: {str(e)}'}), 500

@spectrometer_bp.route('/validate_angle', methods=['POST'])
def validate_angle():
    """验证角度读数"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        student_reading = data.get('student_reading')
        correct_reading = data.get('correct_reading')
        tolerance = data.get('tolerance', 0.5)  # 默认容差30角秒

        if student_reading is None or correct_reading is None:
            return jsonify({'error': '缺少角度读数数据'}), 400

        is_valid, error_message = validate_angle_measurement(
            student_reading, correct_reading, tolerance
        )

        return jsonify({
            'is_valid': is_valid,
            'error_message': error_message,
            'student_reading': student_reading,
            'correct_reading': correct_reading,
            'difference': abs(student_reading - correct_reading)
        })

    except Exception as e:
        print(f"验证角度错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'验证角度时发生错误: {str(e)}'}), 500

@spectrometer_bp.route('/check_adjustment', methods=['POST'])
def check_adjustment():
    """AI检查仪器调节步骤"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        model_id = data.get('model_id', '')
        adjustment_type = data.get('adjustment_type', 'telescope')
        adjustment_data = data.get('adjustment_data', {})

        # 创建检查提示词
        check_prompt = create_adjustment_check_prompt(adjustment_type, adjustment_data)

        # 调用AI检查
        try:
            check_result = model_registry.get_analysis(check_prompt, model_id)
            return jsonify({'check_result': check_result})
        except Exception as ai_error:
            print(f"AI检查错误: {ai_error}")
            return jsonify({'error': f'AI检查失败: {str(ai_error)}'}), 500

    except Exception as e:
        print(f"检查调节错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'检查调节时发生错误: {str(e)}'}), 500

@spectrometer_bp.route('/analyze', methods=['POST'])
def analyze_data():
    """AI分析分光计实验数据"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '没有接收到数据'}), 400

        # 获取模型ID
        model_id = data.get('model_id', '')

        # 准备分析提示词
        analysis_prompt = create_analysis_prompt(data)

        # 调用AI分析
        try:
            analysis_result = model_registry.get_analysis(analysis_prompt, model_id)
            return jsonify({'analysis': analysis_result})
        except Exception as ai_error:
            print(f"AI分析错误: {ai_error}")
            return jsonify({'error': f'AI分析失败: {str(ai_error)}'}), 500

    except Exception as e:
        print(f"数据分析错误: {e}")
        traceback.print_exc()
        return jsonify({'error': f'数据分析时发生错误: {str(e)}'}), 500

def create_adjustment_check_prompt(adjustment_type, adjustment_data):
    """创建用于AI检查仪器调节的提示词"""
    prompt = f"""# 分光计仪器调节检查

## 调节类型
{adjustment_type}

## 调节数据
"""

    for key, value in adjustment_data.items():
        prompt += f"- {key}: {value}\n"

    prompt += f"""
## 检查要求

请作为一位光学实验教师，检查学生的分光计{adjustment_type}调节：

1. **调节正确性**：调节步骤是否正确？
2. **操作规范性**：是否遵循了正确的操作顺序？
3. **精度要求**：是否达到了实验要求的精度？
4. **常见问题**：指出可能存在的问题和改进方法
5. **操作建议**：给出具体的操作建议和注意事项

请用专业而友善的语气，既要指出问题，也要给予鼓励。
"""

    return prompt

def create_analysis_prompt(data):
    """创建用于AI分析的提示词"""
    prompt = """# 分光计实验数据分析

## 实验背景
这是一个分光计的调节与使用实验，主要内容包括：
- 分光计的系统调节（望远镜、平行光管、载物台）
- 三棱镜最小偏向角法测量折射率
- 角度测量技术和精度分析

## 实验数据

"""

    # 添加仪器调节数据
    if 'telescope_adjustment' in data:
        prompt += "### 望远镜调节\n"
        prompt += f"- 调节状态: {'已完成' if data['telescope_adjustment'] else '未完成'}\n"

    if 'collimator_adjustment' in data:
        prompt += "### 平行光管调节\n"
        prompt += f"- 调节状态: {'已完成' if data['collimator_adjustment'] else '未完成'}\n"

    if 'platform_adjustment' in data:
        prompt += "### 载物台调节\n"
        prompt += f"- 调节状态: {'已完成' if data['platform_adjustment'] else '未完成'}\n"

    # 添加三棱镜测量数据
    if 'prism_measurements' in data:
        prism_data = data['prism_measurements']
        prompt += "### 三棱镜测量数据\n"

        if prism_data.get('deviations'):
            prompt += "**偏向角测量:**\n"
            for i, deviation in enumerate(prism_data['deviations'], 1):
                prompt += f"- 第{i}次测量: {deviation}°\n"

        if prism_data.get('apex_angle'):
            prompt += f"**棱镜顶角:** {prism_data['apex_angle']}°\n"

        if prism_data.get('refractive_index'):
            prompt += f"**计算折射率:** {prism_data['refractive_index']}\n"

    prompt += """
## 分析要求

请对以上分光计实验数据进行详细分析，包括：

1. **仪器调节评估**
   - 各部件调节的完整性和正确性
   - 调节对测量精度的影响
   - 系统误差的来源分析

2. **角度测量分析**
   - 角度读数的精度和准确性
   - 最小偏向角的判断是否正确
   - 多次测量的一致性评估

3. **折射率计算**
   - 计算方法的正确性
   - 结果的合理性评估
   - 与理论值的比较

4. **误差分析**
   - 系统误差和随机误差的识别
   - 主要误差来源的分析
   - 不确定度的估算

5. **实验改进建议**
   - 提高测量精度的方法
   - 减小误差的措施
   - 操作技巧的改进

请用专业的光学实验语言进行分析，并给出具体的数值计算和结论。
"""

    return prompt