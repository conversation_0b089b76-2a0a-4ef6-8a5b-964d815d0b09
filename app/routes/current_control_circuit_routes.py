# app/routes/current_control_circuit_routes.py
from flask import Blueprint, request, jsonify
from utils.current_control_circuit_utils import generate_zhiliu_plot, preprocess_data_zhiliu, analyze_zhiliu_data_with_ai
import traceback # For better error logging

current_control_circuit_bp = Blueprint('current_control_circuit', __name__, url_prefix='/current_control_circuit')

@current_control_circuit_bp.route('/plot', methods=['POST'])
def zhiliu_plot_route():
    try:
        data = request.get_json()
        if not data or 'k1_current' not in data or 'k01_current' not in data:
            return jsonify({'error': 'Missing data for plotting.'}), 400

        k1_current = [float(x) for x in data['k1_current']]
        k01_current = [float(x) for x in data['k01_current']]

        plot_url = generate_zhiliu_plot(k1_current, k01_current)
        return jsonify({'plot_url': plot_url})
    except ValueError:
        return jsonify({'error': 'Invalid data format for currents. Must be numbers.'}), 400
    except Exception as e:
        traceback.print_exc()
        return jsonify({'error': f'Error generating plot: {str(e)}'}), 500

@current_control_circuit_bp.route('/analyze', methods=['POST'])
def zhiliu_analyze_route():
    try:
        data = request.get_json()
        if not data or 'k1_current' not in data or 'k01_current' not in data:
            return jsonify({'error': 'Missing data for analysis.'}), 400

        k1_current = [float(x) for x in data['k1_current']]
        k01_current = [float(x) for x in data['k01_current']]
        selected_model_id = data.get('model_id')

        processed_data = preprocess_data_zhiliu(k1_current, k01_current)
        analysis_result = analyze_zhiliu_data_with_ai(k1_current, k01_current, processed_data, selected_model_id)

        # Return as plain text for direct rendering by showdown.js
        return analysis_result, 200, {'Content-Type': 'text/plain; charset=utf-8'}
    except ValueError:
        return "数据格式错误，电流值必须是数字。", 400, {'Content-Type': 'text/plain; charset=utf-8'}
    except Exception as e:
        traceback.print_exc()
        # Return error as plain text
        return f"分析过程中发生错误: {str(e)}", 500, {'Content-Type': 'text/plain; charset=utf-8'}
