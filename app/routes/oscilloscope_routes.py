# app/routes/oscilloscope_routes.py
from flask import Blueprint, request, jsonify
from utils.oscilloscope_utils import analyze_oscilloscope_data_with_ai
import traceback

oscilloscope_bp = Blueprint('oscilloscope', __name__, url_prefix='/oscilloscope')

@oscilloscope_bp.route('/analyze-measurement', methods=['POST'])
def oscilloscope_analyze_route():
    try:
        data = request.json
        selected_model_id = data.get('model_id')

        generator_voltage = float(data['generator']['voltage'])
        generator_frequency = float(data['generator']['frequency'])
        measured_voltage = float(data['oscilloscope']['measuredVoltage'])
        measured_frequency = float(data['oscilloscope']['measuredFrequency'])

        voltage_error = abs((measured_voltage - generator_voltage) / generator_voltage) * 100 if generator_voltage != 0 else float('inf')
        frequency_error = abs((measured_frequency - generator_frequency) / generator_frequency) * 100 if generator_frequency != 0 else float('inf')

        analysis_result = analyze_oscilloscope_data_with_ai(
            generator_voltage, generator_frequency,
            measured_voltage, measured_frequency,
            voltage_error, frequency_error, selected_model_id
        )
        # Return as plain text for direct rendering by showdown.js
        return analysis_result, 200, {'Content-Type': 'text/plain; charset=utf-8'}
    except KeyError as e:
        return f"请求数据中缺少键: {str(e)}", 400, {'Content-Type': 'text/plain; charset=utf-8'}
    except ValueError:
        return "数据格式错误，电压和频率值必须是数字。", 400, {'Content-Type': 'text/plain; charset=utf-8'}
    except Exception as e:
        traceback.print_exc()
        return f"分析过程中发生错误: {str(e)}", 500, {'Content-Type': 'text/plain; charset=utf-8'}
