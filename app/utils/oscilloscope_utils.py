# app/utils/oscilloscope_utils.py
from .common_utils import get_openai_client_and_model
from datetime import datetime
def analyze_oscilloscope_data_with_ai(gen_voltage, gen_freq, meas_voltage, meas_freq, v_error, f_error, selected_model_id=None):
    """使用AI分析示波器数据"""
    try:
        client, model_name = get_openai_client_and_model(selected_model_id)
        prompt = f"""
请分析以下示波器测量数据：

**测量数据：**
- 函数发生器设置电压：{gen_voltage} Vpp
- 函数发生器设置频率：{gen_freq} Hz
- 示波器测量电压：{meas_voltage} Vpp
- 示波器测量频率：{meas_freq} Hz
- 电压误差：{v_error:.2f}%
- 频率误差：{f_error:.2f}%

**请从以下方面进行分析：**
1.  **测量精度评估**：
    *   评价电压测量的准确性。
    *   评价频率测量的准确性。
    *   综合评价整体测量精度。
2.  **误差分析**：
    *   针对电压误差，列出至少3个可能的具体原因（例如：探头校准不良、仪器本身精度限制、读数误差、连接线质量等）。
    *   针对频率误差，列出至少3个可能的具体原因（例如：函数发生器频率漂移、示波器时基不准、触发设置不当导致周期测量不准等）。
3.  **实验结论**：
    *   总结本次测量的主要结果。
    *   明确指出实验数据是否在可接受范围内（例如，通常教学实验允许5-10%的误差，请以此为参考）。如果误差在10%以内，则认为测量<font color='green'>通过</font>；否则认为测量<font color='red'>不通过</font>，建议检查并重试。

请用中文回答，使用Markdown格式。直接输出分析结果，不要有额外的开场白或总结性发言。
"""
        response = client.chat.completions.create(
            model=model_name,
            messages=[{"role": "user", "content": prompt}],
            temperature=0.5, # Slightly higher for more descriptive analysis
            max_tokens=2000
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"Error in AI analysis for oscilloscope: {e}")
        return f"AI分析失败：{str(e)}"


def prepare_oscilloscope_for_db(data_from_frontend):
    """
    Packages oscilloscope experiment data into the standardized JSON structure for DB submission.
    """
    student_id = data_from_frontend.get('student_id')
    student_name = data_from_frontend.get('student_name')
    measurement_data = data_from_frontend.get('measurement_data', {})
    ai_analysis_text = data_from_frontend.get('analysis_result', "AI分析未执行或未提供。")
    selected_model_id = data_from_frontend.get('model_id', 'default')

    if not all([student_id, student_name]):
        raise ValueError("学生ID或姓名缺失。")
    if not measurement_data:
        raise ValueError("示波器测量数据缺失。")

    generator_data = measurement_data.get('generator', {})
    oscilloscope_data = measurement_data.get('oscilloscope', {})

    if not generator_data or not oscilloscope_data:
        raise ValueError("函数发生器或示波器数据不完整。")

    try:
        # Ensure values are float, use .get with default to avoid KeyError if keys are missing
        gen_voltage = float(generator_data.get('voltage', 0))
        gen_freq = float(generator_data.get('frequency', 0))
        meas_voltage = float(oscilloscope_data.get('measuredVoltage', 0))
        meas_freq = float(oscilloscope_data.get('measuredFrequency', 0))
    except (ValueError, TypeError) as e:
        raise ValueError(f"电压或频率数据格式无效: {e}")

    voltage_error_percent = abs((meas_voltage - gen_voltage) / gen_voltage) * 100 if gen_voltage != 0 else float('inf')
    frequency_error_percent = abs((meas_freq - gen_freq) / gen_freq) * 100 if gen_freq != 0 else float('inf')

    # Determine conclusion
    derived_conclusion = '不通过' # Default
    if isinstance(ai_analysis_text, str):
        if "<font color='green'>通过</font>" in ai_analysis_text:
            derived_conclusion = '通过'
        elif "<font color='red'>不通过</font>" in ai_analysis_text or "<font color='red'>重新做</font>" in ai_analysis_text:
            derived_conclusion = '不通过'
        # Fallback to error thresholds if AI text is not conclusive
        elif voltage_error_percent <= 10 and frequency_error_percent <= 10:
             derived_conclusion = '通过'

    experiment_data_payload = {
        "measurement_data": {
            "generator": generator_data,
            "oscilloscope": oscilloscope_data
        },
        "errors": {
            "voltage": round(voltage_error_percent, 2) if voltage_error_percent != float('inf') else 'inf',
            "frequency": round(frequency_error_percent, 2) if frequency_error_percent != float('inf') else 'inf'
        },
        "analysis": {
            "model_id_used": selected_model_id,
            "text": ai_analysis_text,
            "conclusion": derived_conclusion
        },
    }
    return experiment_data_payload
