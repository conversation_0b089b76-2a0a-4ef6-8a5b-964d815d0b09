import matplotlib.pyplot as plt
import numpy as np
import base64
from io import BytesIO
from datetime import datetime
import json
import math
from utils.common_utils import generate_experiment_plot_base

def calculate_uncertainty_components(data, instrument_error):
    """
    计算不确定度的各个分量
    返回: (平均值, A类不确定度, B类不确定度, C类不确定度)
    """
    n = len(data)
    mean_value = np.mean(data)

    # A类不确定度计算
    if n > 1:
        std_dev = np.std(data, ddof=1)  # 样本标准差
        ua = std_dev / math.sqrt(n)     # A类不确定度
    else:
        ua = 0

    # B类不确定度（仪器误差）
    ub = instrument_error / math.sqrt(3)  # 均匀分布

    # C类不确定度（合成不确定度）
    uc = math.sqrt(ua**2 + ub**2)

    return mean_value, ua, ub, uc

def generate_changduceliang_plot(data):
    """
    生成长度测量实验的数据可视化图表
    """
    try:
        # 创建图形
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
        fig.suptitle('长度测量实验数据分析与不确定度', fontsize=16, fontweight='bold')

        # 1. 游标卡尺测量数据及不确定度
        if 'vernier_data' in data:
            vernier_data = data['vernier_data']
            measurements = ['外径D1', '内径D2', '高度H']
            values = [vernier_data['outer_diameter'], vernier_data['inner_diameter'], vernier_data['height']]

            means = []
            uncertainties = []

            for val in values:
                mean_val, ua, ub, uc = calculate_uncertainty_components(val, 0.02)
                means.append(mean_val)
                uncertainties.append(uc)

            x_pos = np.arange(len(measurements))
            bars = ax1.bar(x_pos, means, yerr=uncertainties, capsize=5, alpha=0.7, 
                          color=['#3498db', '#e74c3c', '#2ecc71'])
            ax1.set_xlabel('测量项目')
            ax1.set_ylabel('测量值 (mm)')
            ax1.set_title('游标卡尺测量结果（含不确定度）')
            ax1.set_xticks(x_pos)
            ax1.set_xticklabels(measurements)
            ax1.grid(True, alpha=0.3)

            # 添加数值标签
            for i, (mean, unc) in enumerate(zip(means, uncertainties)):
                ax1.text(i, mean + unc + 0.1, f'{mean:.3f}±{unc:.3f}', 
                        ha='center', va='bottom', fontsize=9)

        # 2. 螺旋测微计测量数据分布
        if 'micrometer_data' in data:
            micrometer_data = data['micrometer_data']
            diameters = micrometer_data['diameter']

            # 绘制测量值散点图
            x_points = range(1, len(diameters) + 1)
            ax2.scatter(x_points, diameters, color='#e74c3c', s=60, alpha=0.7)
            ax2.plot(x_points, diameters, color='#e74c3c', alpha=0.5, linestyle='--')

            # 计算不确定度
            mean_diameter, ua, ub, uc = calculate_uncertainty_components(diameters, 0.004)

            # 添加平均线和不确定度区间
            ax2.axhline(y=mean_diameter, color='#2ecc71', linestyle='-', linewidth=2, 
                       label=f'平均值: {mean_diameter:.4f} mm')
            ax2.axhline(y=mean_diameter + uc, color='#f39c12', linestyle='--', alpha=0.7,
                       label=f'不确定度: ±{uc:.4f} mm')
            ax2.axhline(y=mean_diameter - uc, color='#f39c12', linestyle='--', alpha=0.7)

            ax2.set_xlabel('测量次数')
            ax2.set_ylabel('直径 (mm)')
            ax2.set_title('螺旋测微计测量小球直径')
            ax2.grid(True, alpha=0.3)
            ax2.legend()
            ax2.set_xticks(x_points)

        # 3. 不确定度分量对比
        if 'uncertainty_analysis' in data:
            uncertainty_data = data['uncertainty_analysis']
            instruments = list(uncertainty_data.keys())
            ua_values = [uncertainty_data[inst]['ua'] for inst in instruments]
            ub_values = [uncertainty_data[inst]['ub'] for inst in instruments]
            uc_values = [uncertainty_data[inst]['uc'] for inst in instruments]

            x = np.arange(len(instruments))
            width = 0.25

            ax3.bar(x - width, ua_values, width, label='A类不确定度', alpha=0.8, color='#3498db')
            ax3.bar(x, ub_values, width, label='B类不确定度', alpha=0.8, color='#e74c3c')
            ax3.bar(x + width, uc_values, width, label='C类不确定度', alpha=0.8, color='#2ecc71')

            ax3.set_xlabel('测量仪器')
            ax3.set_ylabel('不确定度 (mm)')
            ax3.set_title('不确定度分量对比')
            ax3.set_xticks(x)
            ax3.set_xticklabels(instruments)
            ax3.legend()
            ax3.grid(True, alpha=0.3)

        # 4. 体积测量结果及其不确定度
        if 'volume_results' in data:
            volume_data = data['volume_results']
            objects = list(volume_data.keys())
            volumes = [volume_data[obj]['volume'] for obj in objects]
            volume_uncertainties = [volume_data[obj]['uncertainty'] for obj in objects]

            x_pos = range(len(objects))
            bars = ax4.bar(x_pos, volumes, yerr=volume_uncertainties, capsize=5, alpha=0.7,
                          color=['#9b59b6', '#f39c12', '#1abc9c'][:len(objects)])
            ax4.set_xlabel('测量对象')
            ax4.set_ylabel('体积 (mm³)')
            ax4.set_title('体积计算结果（含不确定度）')
            ax4.set_xticks(x_pos)
            ax4.set_xticklabels(objects)
            ax4.grid(True, alpha=0.3)

            # 添加数值标签
            for i, (vol, unc) in enumerate(zip(volumes, volume_uncertainties)):
                ax4.text(i, vol + unc + max(volumes) * 0.02, f'{vol:.1f}±{unc:.1f}', 
                        ha='center', va='bottom', fontsize=9)

        plt.tight_layout()

        # 保存图片到base64
        plot_data = generate_experiment_plot_base(fig, dpi=300)
        return plot_data

    except Exception as e:
        print(f"生成图表时出错: {e}")
        raise e

def prepare_changduceliang_for_db(data_from_frontend):
    """
    准备长度测量实验数据用于数据库存储
    """
    try:
        # 验证必要字段
        required_fields = ['student_id', 'student_name']
        for field in required_fields:
            if not data_from_frontend.get(field):
                raise ValueError(f"缺少必要字段: {field}")

        # 提取学生信息
        student_info = {
            'id': data_from_frontend['student_id'],
            'name': data_from_frontend['student_name']
        }

        # 提取实验数据和不确定度计算结果
        experiment_data = {}

        # 游标卡尺数据
        if 'vernier_data' in data_from_frontend:
            vernier_data = data_from_frontend['vernier_data']
            experiment_data['vernier_caliper'] = {
                'zero_reading': vernier_data.get('zero_reading', 0),
                'instrument_error': 0.02,
                'outer_diameter': vernier_data.get('outer_diameter', []),
                'inner_diameter': vernier_data.get('inner_diameter', []),
                'height': vernier_data.get('height', []),
                'uncertainty_calculations': vernier_data.get('uncertainty_calculations', {})
            }

        # 螺旋测微计数据
        if 'micrometer_data' in data_from_frontend:
            micrometer_data = data_from_frontend['micrometer_data']
            experiment_data['micrometer'] = {
                'zero_reading': micrometer_data.get('zero_reading', 0),
                'instrument_error': 0.004,
                'diameter': micrometer_data.get('diameter', []),
                'uncertainty_calculations': micrometer_data.get('uncertainty_calculations', {})
            }

        # 数字式测量工具数据
        if 'digital_data' in data_from_frontend:
            digital_data = data_from_frontend['digital_data']
            experiment_data['digital_instruments'] = {
                'length': digital_data.get('length', []),
                'diameter': digital_data.get('diameter', []),
                'uncertainty_calculations': digital_data.get('uncertainty_calculations', {})
            }

        # 组装完整的数据包
        complete_data_package = {
            'experiment_info': {
                'type': 'changduceliang',
                'name': '长度测量实验',
                'submission_timestamp_utc': datetime.utcnow().isoformat() + 'Z'
            },
            'student_info': student_info,
            'raw_data': experiment_data,
            'uncertainty_analysis': data_from_frontend.get('uncertainty_analysis', {}),
            'volume_calculations': data_from_frontend.get('volume_calculations', {}),
            'plot_data': data_from_frontend.get('plot_data'),
            'analysis_result': data_from_frontend.get('analysis_result')
        }

        return complete_data_package

    except Exception as e:
        raise ValueError(f"数据打包失败: {str(e)}")
