# db_connector.py
import pymysql
import os
import logging
from typing import Dict, List, Optional, Tuple
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

logger = logging.getLogger(__name__)

class ModelRegistryConnector:
    """模型注册表数据库连接器"""

    def __init__(self):
        self.db_config = {
            'host': os.getenv('DB_HOST'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER'),
            'password': os.getenv('DB_PASSWORD'),
            'database': os.getenv('DB_NAME'),
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
        self.app_name = os.getenv('APP_NAME', 'Physics_experiments')

    def get_connection(self):
        """获取数据库连接"""
        try:
            return pymysql.connect(**self.db_config)
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return None

    def get_app_models(self) -> List[Dict]:
        """获取应用可用的模型列表"""
        connection = self.get_connection()
        if not connection:
            return []

        try:
            with connection.cursor() as cursor:
                sql = """
                SELECT
                    am.id as app_model_id,
                    am.is_default,
                    m.id as model_id,
                    m.display_name,
                    m.internal_name,
                    m.api_endpoint,
                    m.input_token_price,
                    m.output_token_price,
                    m.input_picture_price,
                    m.free,
                    m.high_price,
                    p.name as platform_name,
                    p.base_url,
                    p.api_key
                FROM app_models am
                JOIN ai_models m ON am.model_id = m.id
                JOIN platforms p ON m.platform_id = p.id
                JOIN applications a ON am.application_id = a.id
                WHERE a.name = %s
                ORDER BY am.is_default DESC, m.display_name
                """
                cursor.execute(sql, (self.app_name,))
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取应用模型失败: {e}")
            return []
        finally:
            connection.close()

    def get_default_model(self) -> Optional[Dict]:
        """获取默认模型"""
        models = self.get_app_models()
        if not models:
            return None

        # 查找默认模型
        for model in models:
            if model['is_default']:
                return model

        # 如果没有默认模型，返回第一个
        return models[0] if models else None

    def get_model_config(self, model_id: int = None) -> Tuple[str, str, str]:
        """
        获取模型配置
        返回: (api_key, base_url, model_name)
        """
        if model_id:
            # 根据模型ID获取特定模型
            connection = self.get_connection()
            if connection:
                try:
                    with connection.cursor() as cursor:
                        sql = """
                        SELECT
                            m.internal_name,
                            p.base_url,
                            p.api_key
                        FROM ai_models m
                        JOIN platforms p ON m.platform_id = p.id
                        WHERE m.id = %s
                        """
                        cursor.execute(sql, (model_id,))
                        result = cursor.fetchone()
                        if result:
                            return (
                                result['api_key'],
                                result['base_url'],
                                result['internal_name']
                            )
                except Exception as e:
                    logger.error(f"获取模型配置失败: {e}")
                finally:
                    connection.close()

        # 获取默认模型
        default_model = self.get_default_model()
        if default_model:
            return (
                default_model['api_key'],
                default_model['base_url'],
                default_model['internal_name']
            )

        # 备用配置
        return (
            os.getenv('BACKUP_OPENAI_API_KEY'),
            os.getenv('BACKUP_OPENAI_API_BASE'),
            os.getenv('BACKUP_MODEL_NAME')
        )


class ExperimentDBConnector:
    """实验结果数据库连接器"""

    def __init__(self):
        # 使用相同的环境变量，但连接到不同的数据库
        self.db_config = {
            'host': os.getenv('DB_HOST'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER'),
            'password': os.getenv('DB_PASSWORD'),
            'database': os.getenv('EXPERIMENT_DB_NAME', 'pedb'),  # 实验结果数据库名
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }

    def get_connection(self):
        """获取实验结果数据库连接"""
        try:
            return pymysql.connect(**self.db_config)
        except Exception as e:
            logger.error(f"实验数据库连接失败: {e}")
            return None


# 全局实例
model_registry = ModelRegistryConnector()
experiment_db = ExperimentDBConnector()
