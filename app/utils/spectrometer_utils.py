import matplotlib.pyplot as plt
import numpy as np
import base64
import io
import json
from datetime import datetime
import math
from utils.common_utils import generate_experiment_plot_base

def generate_spectrometer_plot(data):
    """生成分光计实验图表"""
    try:
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('分光计实验数据分析', fontsize=16, fontweight='bold')

        # 1. 偏向角测量图
        if 'prism_measurements' in data and 'deviations' in data['prism_measurements']:
            deviations = data['prism_measurements']['deviations']
            measurements = list(range(1, len(deviations) + 1))

            ax1.plot(measurements, deviations, 'bo-', linewidth=2, markersize=8)
            ax1.set_xlabel('测量次数')
            ax1.set_ylabel('偏向角 (°)')
            ax1.set_title('偏向角测量结果')
            ax1.grid(True, alpha=0.3)

            # 添加平均值线
            if deviations:
                mean_deviation = np.mean(deviations)
                ax1.axhline(y=mean_deviation, color='r', linestyle='--', 
                           label=f'平均值: {mean_deviation:.2f}°')
                ax1.legend()

        # 2. 角度读数精度分析
        angle_errors = np.random.normal(0, 0.5, 20)  # 模拟角度读数误差
        ax2.hist(angle_errors, bins=10, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.set_xlabel('角度读数误差 (′)')
        ax2.set_ylabel('频次')
        ax2.set_title('角度读数精度分布')
        ax2.grid(True, alpha=0.3)

        # 3. 折射率随波长变化（理论曲线）
        wavelengths = np.linspace(400, 700, 100)  # 可见光波长范围
        # 使用柯西色散公式
        A, B = 1.5, 0.01  # 典型玻璃参数
        n_wavelength = A + B / (wavelengths/1000)**2

        ax3.plot(wavelengths, n_wavelength, 'g-', linewidth=2)
        ax3.set_xlabel('波长 (nm)')
        ax3.set_ylabel('折射率')
        ax3.set_title('玻璃折射率色散曲线')
        ax3.grid(True, alpha=0.3)

        # 标记钠黄线位置
        ax3.axvline(x=589.3, color='orange', linestyle='--', label='钠黄线 (589.3nm)')
        ax3.legend()

        # 4. 实验精度分析
        instruments = ['分光计', '游标卡尺', '螺旋测微计']
        precisions = [0.5, 1.0, 0.2]  # 角分精度

        bars = ax4.bar(instruments, precisions, color=['lightcoral', 'lightblue', 'lightgreen'])
        ax4.set_ylabel('精度 (′)')
        ax4.set_title('不同仪器精度比较')
        ax4.grid(True, alpha=0.3, axis='y')

        # 在柱状图上添加数值标签
        for bar, precision in zip(bars, precisions):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{precision}′', ha='center', va='bottom')

        plt.tight_layout()
        # 保存图片为base64
        plot_data = generate_experiment_plot_base(fig, dpi=300)
        return plot_data

    except Exception as e:
        print(f"生成分光计图表错误: {e}")
        raise

def calculate_refractive_index(apex_angle_deg, min_deviation_deg):
    """计算折射率"""
    # 将角度转换为弧度
    A = math.radians(apex_angle_deg)
    delta_m = math.radians(min_deviation_deg)

    # 计算折射率
    n = math.sin((A + delta_m) / 2) / math.sin(A / 2)

    return n

def calculate_apex_angle(reflection_angle_1, reflection_angle_2):
    """计算三棱镜顶角"""
    return abs(reflection_angle_1 - reflection_angle_2) / 2

def validate_angle_measurement(student_reading, correct_reading, tolerance=0.5):
    """验证角度读数"""
    difference = abs(student_reading - correct_reading)

    if difference <= tolerance:
        return True, "读数正确"
    else:
        return False, f"读数误差过大，误差为 {difference:.2f}′，超出容差范围 ±{tolerance}′"

def prepare_spectrometer_for_db(data_from_frontend):
    """准备分光计实验数据用于数据库存储"""
    try:
        # 验证必要字段
        required_fields = ['student_id', 'student_name', 'experiment_type']
        for field in required_fields:
            if field not in data_from_frontend:
                raise ValueError(f"缺少必要字段: {field}")

        # 构建标准化的数据结构
        experiment_data = {
            'experiment_info': {
                'type': 'spectrometer',
                'title': '分光计的调节与使用实验',
                'submission_timestamp_utc': datetime.utcnow().isoformat() + 'Z'
            },
            'student_info': {
                'id': data_from_frontend['student_id'],
                'name': data_from_frontend['student_name']
            },
            'instrument_adjustments': {
                'telescope_adjustment': data_from_frontend.get('telescope_adjustment', False),
                'collimator_adjustment': data_from_frontend.get('collimator_adjustment', False),
                'platform_adjustment': data_from_frontend.get('platform_adjustment', False)
            },
            'measurements': {
                'prism_measurements': data_from_frontend.get('prism_measurements', {}),
                'angle_readings': data_from_frontend.get('angle_readings', []),
                'refractive_index_results': data_from_frontend.get('refractive_index_results', {})
            },
            'analysis_results': {
                'precision_analysis': data_from_frontend.get('precision_analysis', {}),
                'error_analysis': data_from_frontend.get('error_analysis', {}),
                'final_conclusions': data_from_frontend.get('final_conclusions', {})
            }
        }

        return experiment_data

    except Exception as e:
        raise ValueError(f"分光计实验数据处理错误: {str(e)}")