import matplotlib.pyplot as plt
import numpy as np
import base64
from io import BytesIO

def generate_micro_length_lever_plot(n_list, s_list, L1, L2, D):
    """
    生成纸厚与层数关系图，返回base64图片
    """
    n_arr = np.array(n_list)
    s_arr = np.array(s_list)
    d_arr = s_arr * L1 * L2 / (2 * n_arr * D)
    fig, ax = plt.subplots(figsize=(7,5))
    ax.plot(n_arr, d_arr, 'o-', color='#3498db', label='每组纸厚d')
    ax.set_xlabel('纸层数 n')
    ax.set_ylabel('纸厚 d (mm)')
    ax.set_title('光杠杆法测纸厚实验结果')
    for i, (n, d) in enumerate(zip(n_arr, d_arr)):
        ax.text(n, d, f'{d:.4f}', ha='center', va='bottom', fontsize=9)
    ax.grid(True, alpha=0.3)
    ax.legend()
    plt.tight_layout()
    buf = BytesIO()
    plt.savefig(buf, format='png', dpi=200)
    plt.close(fig)
    buf.seek(0)
    img_base64 = base64.b64encode(buf.read()).decode('utf-8')
    return img_base64

# AI分析函数（可根据实际AI接口完善）
def analyze_micro_length_lever_data_with_ai(n_list, s_list, L1, L2, D, model_id=None):
    d_arr = np.array(s_list) * L1 * L2 / (2 * np.array(n_list) * D)
    mean_d = np.mean(d_arr)
    std_d = np.std(d_arr, ddof=1) if len(d_arr) > 1 else 0
    prompt = f"""
# 微小长度测量（光杠杆法测纸厚）实验数据分析

## 实验数据
- L1（长臂）: {L1} mm
- L2（短臂）: {L2} mm
- 各组纸层数: {n_list}
- 各组反射点位移: {s_list} mm
- 计算得到各组纸厚: {[round(x, 5) for x in d_arr]} mm
- 平均纸厚: {mean_d:.5f} mm
- 标准差: {std_d:.5f} mm

## 分析要求
请分析本实验数据，评价实验结果的合理性、误差来源、改进建议，并用简明中文总结。
"""
    # 这里直接返回prompt，实际可调用AI接口
    return prompt 