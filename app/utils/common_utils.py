import os
import openai
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import pymysql
import logging
from typing import Dict, List, Optional, Tuple
from dotenv import load_dotenv
import io, base64

# 加载环境变量
load_dotenv()
logger = logging.getLogger(__name__)

class ModelRegistryConnector:
    """模型注册表数据库连接器"""

    def __init__(self):
        self.db_config = {
            'host': os.getenv('DB_HOST'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER'),
            'password': os.getenv('DB_PASSWORD'),
            'database': os.getenv('DB_NAME'),
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }
        self.app_name = os.getenv('APP_NAME', 'Physics_experiments')

    def get_connection(self):
        """获取数据库连接"""
        try:
            return pymysql.connect(**self.db_config)
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return None

    def get_app_models(self) -> List[Dict]:
        """获取应用可用的模型列表"""
        connection = self.get_connection()
        if not connection:
            return []

        try:
            with connection.cursor() as cursor:
                sql = """
                SELECT
                    am.id as app_model_id,
                    am.is_default,
                    m.id as model_id,
                    m.display_name,
                    m.internal_name,
                    m.api_endpoint,
                    m.input_token_price,
                    m.output_token_price,
                    m.input_picture_price,
                    m.free,
                    m.high_price,
                    p.name as platform_name,
                    p.base_url,
                    p.api_key
                FROM app_models am
                JOIN ai_models m ON am.model_id = m.id
                JOIN platforms p ON m.platform_id = p.id
                JOIN applications a ON am.application_id = a.id
                WHERE a.name = %s
                ORDER BY am.is_default DESC, m.display_name
                """
                cursor.execute(sql, (self.app_name,))
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取应用模型失败: {e}")
            return []
        finally:
            connection.close()

    def get_default_model(self) -> Optional[Dict]:
        """获取默认模型"""
        models = self.get_app_models()
        if not models:
            return None

        # 查找默认模型
        for model in models:
            if model['is_default']:
                return model

        # 如果没有默认模型，返回第一个
        return models[0] if models else None

    def get_model_config(self, model_id: int = None) -> Tuple[str, str, str]:
        """
        获取模型配置
        返回: (api_key, base_url, model_name)
        """
        if model_id:
            # 根据模型ID获取特定模型
            connection = self.get_connection()
            if connection:
                try:
                    with connection.cursor() as cursor:
                        sql = """
                        SELECT
                            m.internal_name,
                            p.base_url,
                            p.api_key
                        FROM ai_models m
                        JOIN platforms p ON m.platform_id = p.id
                        WHERE m.id = %s
                        """
                        cursor.execute(sql, (model_id,))
                        result = cursor.fetchone()
                        if result:
                            return (
                                result['api_key'],
                                result['base_url'],
                                result['internal_name']
                            )
                except Exception as e:
                    logger.error(f"获取模型配置失败: {e}")
                finally:
                    connection.close()

        # 获取默认模型
        default_model = self.get_default_model()
        if default_model:
            return (
                default_model['api_key'],
                default_model['base_url'],
                default_model['internal_name']
            )

        # 备用配置
        return (
            os.getenv('BACKUP_OPENAI_API_KEY'),
            os.getenv('BACKUP_OPENAI_API_BASE'),
            os.getenv('BACKUP_MODEL_NAME')
        )

    def get_analysis(self, prompt: str, model_id: str = None) -> str:
        """获取AI分析结果"""
        try:
            client, model_name = get_openai_client_and_model(model_id)

            response = client.chat.completions.create(
                model=model_name,
                messages=[
                    {"role": "system", "content": "你是一位专业的物理实验教师，擅长数据分析和实验指导。请用专业、准确的语言进行分析。"},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=2000
            )

            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            raise Exception(f"AI分析失败: {str(e)}")


class ExperimentDBConnector:
    """实验结果数据库连接器"""

    def __init__(self):
        # 使用相同的环境变量，但连接到不同的数据库
        self.db_config = {
            'host': os.getenv('DB_HOST'),
            'port': int(os.getenv('DB_PORT', 3306)),
            'user': os.getenv('DB_USER'),
            'password': os.getenv('DB_PASSWORD'),
            'database': os.getenv('EXPERIMENT_DB_NAME', 'pedb'),  # 实验结果数据库名
            'charset': 'utf8mb4',
            'cursorclass': pymysql.cursors.DictCursor
        }

    def get_connection(self):
        """获取实验结果数据库连接"""
        try:
            return pymysql.connect(**self.db_config)
        except Exception as e:
            logger.error(f"实验数据库连接失败: {e}")
            return None


# 全局实例
model_registry = ModelRegistryConnector()
experiment_db = ExperimentDBConnector()

def setup_font():
    """设置中文字体"""
    try:
        # Try to find a common Chinese font, adjust path if necessary
        # For wqy-zenhei.ttc, ensure it's in the same directory or provide full path
        font_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'wqy-zenhei.ttc') # Path relative to app/
        if not os.path.exists(font_path):
            # Fallback for common systems if wqy-zenhei.ttc is not found
            # This part might need adjustment based on your deployment environment
            possible_fonts = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Zen Hei', 'DejaVu Sans']
            found_font = None
            for font_name_check in possible_fonts:
                try:
                    if fm.findfont(fm.FontProperties(family=font_name_check)):
                        font_path = fm.findfont(fm.FontProperties(family=font_name_check))
                        found_font = font_name_check
                        break
                except Exception:
                    continue
            if not found_font:
                print("No suitable Chinese font found. Please install one (e.g., wqy-zenhei) or configure font_path.")
                # Minimal fallback
                plt.rcParams['axes.unicode_minus'] = False
                print("Using system default sans-serif, Chinese characters might not display correctly.")
                return False

        fm.fontManager.addfont(font_path)
        font_prop = fm.FontProperties(fname=font_path)
        font_name = font_prop.get_name()

        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = [font_name] + plt.rcParams['font.sans-serif']
        plt.rcParams['axes.unicode_minus'] = False
        print(f"成功加载字体: {font_name}")
        return True

    except Exception as e:
        print(f"字体设置错误: {str(e)}")
        plt.rcParams['font.family'] = ['sans-serif'] # Default fallback
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
        print("已回退到系统默认字体设置 (SimHei, Microsoft YaHei, etc.).")
        return False

def get_openai_client_and_model(selected_model_id=None):
    """获取OpenAI客户端和模型名称，使用数据库中的配置"""
    api_key, base_url, model_name = model_registry.get_model_config(selected_model_id)
    if not api_key or not base_url:
        raise Exception("无法获取模型配置 (API Key or Base URL missing)")
    if not model_name: # Ensure a model name is always returned
        _, _, default_model_name = model_registry.get_model_config() # Get default if specific one not found
        model_name = default_model_name
        if not model_name:
            raise Exception("无法获取模型名称")

    client = openai.OpenAI(api_key=api_key, base_url=base_url)
    return client, model_name

def generate_experiment_plot_base(fig, dpi=300):
    """
    统一的实验绘图保存工具：
    - 设置好中文字体（自动寻找wqy-zenhei、SimHei等）
    - 关闭负号乱码
    - 保存为base64
    - 自动关闭fig
    """
    # 字体设置（与setup_font类似，保证每次都能用）
    try:
        font_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'wqy-zenhei.ttc')
        if not os.path.exists(font_path):
            possible_fonts = ['SimHei', 'Microsoft YaHei', 'WenQuanYi Zen Hei', 'DejaVu Sans']
            found_font = None
            for font_name_check in possible_fonts:
                try:
                    if fm.findfont(fm.FontProperties(family=font_name_check)):
                        font_path = fm.findfont(fm.FontProperties(family=font_name_check))
                        found_font = font_name_check
                        break
                except Exception:
                    continue
            if not found_font:
                plt.rcParams['axes.unicode_minus'] = False
        else:
            fm.fontManager.addfont(font_path)
            font_prop = fm.FontProperties(fname=font_path)
            font_name = font_prop.get_name()
            plt.rcParams['font.family'] = ['sans-serif']
            plt.rcParams['font.sans-serif'] = [font_name] + plt.rcParams['font.sans-serif']
            plt.rcParams['axes.unicode_minus'] = False
    except Exception:
        plt.rcParams['font.family'] = ['sans-serif']
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
    img_buffer = io.BytesIO()
    fig.savefig(img_buffer, format='png', dpi=dpi, bbox_inches='tight')
    img_buffer.seek(0)
    plot_data = base64.b64encode(img_buffer.getvalue()).decode()
    plt.close(fig)
    return plot_data
