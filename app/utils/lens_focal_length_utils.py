import matplotlib.pyplot as plt
import numpy as np
import base64
from io import BytesIO
from datetime import datetime
import json
import math
from utils.common_utils import generate_experiment_plot_base

def calculate_focal_length_uncertainty(data, instrument_error):
    """
    计算焦距测量的不确定度
    返回: (平均值, A类不确定度, B类不确定度, C类不确定度)
    """
    n = len(data)
    mean_value = np.mean(data)

    # A类不确定度计算
    if n > 1:
        std_dev = np.std(data, ddof=1)  # 样本标准差
        ua = std_dev / math.sqrt(n)     # A类不确定度
    else:
        ua = 0

    # B类不确定度（仪器误差）
    ub = instrument_error / math.sqrt(3)  # 均匀分布

    # C类不确定度（合成不确定度）
    uc = math.sqrt(ua**2 + ub**2)

    return mean_value, ua, ub, uc

def generate_lens_focal_length_plot(data):
    """
    生成薄透镜焦距测定实验的数据可视化图表
    """
    try:
        # 创建图形
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))
        fig.suptitle('薄透镜焦距测定实验数据分析', fontsize=16, fontweight='bold')

        # 1. 凸透镜不同方法测量结果对比
        if 'convex_lens_data' in data:
            convex_data = data['convex_lens_data']
            methods = []
            focal_lengths = []
            uncertainties = []

            if convex_data.get('uv_method'):
                uv_data = convex_data['uv_method']
                methods.append('物距像距法')
                focal_lengths.append(uv_data.get('mean_focal_length', 0))
                uncertainties.append(uv_data.get('std_dev', 0))

            if convex_data.get('autocollimation_method'):
                auto_data = convex_data['autocollimation_method']
                methods.append('自准法')
                focal_lengths.append(auto_data.get('mean_focal_length', 0))
                uncertainties.append(auto_data.get('std_dev', 0))

            if convex_data.get('conjugate_method'):
                conj_data = convex_data['conjugate_method']
                methods.append('共轭法')
                focal_lengths.append(conj_data.get('mean_focal_length', 0))
                uncertainties.append(conj_data.get('std_dev', 0))

            if methods:
                x_pos = np.arange(len(methods))
                bars = ax1.bar(x_pos, focal_lengths, yerr=uncertainties, capsize=5, alpha=0.7, 
                              color=['#3498db', '#e74c3c', '#2ecc71'][:len(methods)])
                ax1.set_xlabel('测量方法')
                ax1.set_ylabel('焦距 (cm)')
                ax1.set_title('凸透镜焦距测量结果对比')
                ax1.set_xticks(x_pos)
                ax1.set_xticklabels(methods)
                ax1.grid(True, alpha=0.3)

                # 添加数值标签
                for i, (focal, unc) in enumerate(zip(focal_lengths, uncertainties)):
                    ax1.text(i, focal + unc + 0.1, f'{focal:.2f}±{unc:.2f}', 
                            ha='center', va='bottom', fontsize=9)

        # 2. 凹透镜不同方法测量结果对比
        if 'concave_lens_data' in data:
            concave_data = data['concave_lens_data']
            methods = []
            focal_lengths = []
            uncertainties = []

            if concave_data.get('uv_method'):
                uv_data = concave_data['uv_method']
                methods.append('物距像距法')
                focal_lengths.append(abs(uv_data.get('mean_focal_length', 0)))
                uncertainties.append(uv_data.get('std_dev', 0))

            if concave_data.get('autocollimation_method'):
                auto_data = concave_data['autocollimation_method']
                methods.append('自准法')
                focal_lengths.append(abs(auto_data.get('mean_focal_length', 0)))
                uncertainties.append(auto_data.get('std_dev', 0))

            if methods:
                x_pos = np.arange(len(methods))
                bars = ax2.bar(x_pos, focal_lengths, yerr=uncertainties, capsize=5, alpha=0.7, 
                              color=['#9b59b6', '#f39c12'][:len(methods)])
                ax2.set_xlabel('测量方法')
                ax2.set_ylabel('焦距绝对值 (cm)')
                ax2.set_title('凹透镜焦距测量结果对比')
                ax2.set_xticks(x_pos)
                ax2.set_xticklabels(methods)
                ax2.grid(True, alpha=0.3)

                # 添加数值标签
                for i, (focal, unc) in enumerate(zip(focal_lengths, uncertainties)):
                    ax2.text(i, focal + unc + 0.1, f'{focal:.2f}±{unc:.2f}', 
                            ha='center', va='bottom', fontsize=9)

        # 3. 测量精度分析
        if 'precision_analysis' in data:
            precision_data = data['precision_analysis']
            lens_types = ['凸透镜', '凹透镜']
            relative_errors = [
                precision_data.get('convex_relative_error', 0),
                precision_data.get('concave_relative_error', 0)
            ]

            bars = ax3.bar(lens_types, relative_errors, alpha=0.7, 
                          color=['#3498db', '#e74c3c'])
            ax3.set_ylabel('相对误差 (%)')
            ax3.set_title('测量精度分析')
            ax3.grid(True, alpha=0.3, axis='y')

            # 添加数值标签
            for i, error in enumerate(relative_errors):
                ax3.text(i, error + 0.1, f'{error:.1f}%', 
                        ha='center', va='bottom', fontsize=10)

        # 4. 不确定度分量分析
        if 'uncertainty_analysis' in data:
            uncertainty_data = data['uncertainty_analysis']
            components = ['A类不确定度', 'B类不确定度', 'C类不确定度']
            ua_values = [uncertainty_data.get('ua_convex', 0), uncertainty_data.get('ua_concave', 0)]
            ub_values = [uncertainty_data.get('ub_convex', 0), uncertainty_data.get('ub_concave', 0)]
            uc_values = [uncertainty_data.get('uc_convex', 0), uncertainty_data.get('uc_concave', 0)]

            x = np.arange(2)
            width = 0.25

            ax4.bar(x - width, ua_values, width, label='A类不确定度', alpha=0.8, color='#3498db')
            ax4.bar(x, ub_values, width, label='B类不确定度', alpha=0.8, color='#e74c3c')
            ax4.bar(x + width, uc_values, width, label='C类不确定度', alpha=0.8, color='#2ecc71')

            ax4.set_xlabel('透镜类型')
            ax4.set_ylabel('不确定度 (cm)')
            ax4.set_title('不确定度分量分析')
            ax4.set_xticks(x)
            ax4.set_xticklabels(['凸透镜', '凹透镜'])
            ax4.legend()
            ax4.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图片到base64
        plot_data = generate_experiment_plot_base(fig, dpi=300)
        return plot_data

    except Exception as e:
        print(f"生成图表时出错: {e}")
        raise e

def analyze_lens_focal_length_data_with_ai(data, selected_model_id=None):
    """使用AI分析薄透镜焦距测定实验数据"""
    try:
        from utils.common_utils import get_openai_client_and_model
        
        client, model_name = get_openai_client_and_model(selected_model_id)
        
        # 构建分析提示词
        prompt = f"""
请分析以下薄透镜焦距测定实验数据：

## 实验数据

### 凸透镜测量结果
"""
        
        if 'convex_lens_data' in data:
            convex_data = data['convex_lens_data']
            if convex_data.get('uv_method'):
                uv_data = convex_data['uv_method']
                prompt += f"- 物距像距法：平均焦距 {uv_data.get('mean_focal_length', 'N/A')} cm，标准差 {uv_data.get('std_dev', 'N/A')} cm\n"
            
            if convex_data.get('autocollimation_method'):
                auto_data = convex_data['autocollimation_method']
                prompt += f"- 自准法：平均焦距 {auto_data.get('mean_focal_length', 'N/A')} cm，标准差 {auto_data.get('std_dev', 'N/A')} cm\n"
            
            if convex_data.get('conjugate_method'):
                conj_data = convex_data['conjugate_method']
                prompt += f"- 共轭法：平均焦距 {conj_data.get('mean_focal_length', 'N/A')} cm，标准差 {conj_data.get('std_dev', 'N/A')} cm\n"

        prompt += "\n### 凹透镜测量结果\n"
        
        if 'concave_lens_data' in data:
            concave_data = data['concave_lens_data']
            if concave_data.get('uv_method'):
                uv_data = concave_data['uv_method']
                prompt += f"- 物距像距法：平均焦距 {uv_data.get('mean_focal_length', 'N/A')} cm，标准差 {uv_data.get('std_dev', 'N/A')} cm\n"
            
            if concave_data.get('autocollimation_method'):
                auto_data = concave_data['autocollimation_method']
                prompt += f"- 自准法：平均焦距 {auto_data.get('mean_focal_length', 'N/A')} cm，标准差 {auto_data.get('std_dev', 'N/A')} cm\n"

        prompt += """
## 分析要求

请从以下几个方面进行分析：

1. **测量方法比较**
   - 评价不同测量方法的优缺点
   - 分析各方法的适用条件和精度
   - 比较不同方法的测量结果一致性

2. **数据质量评估**
   - 评价测量数据的稳定性和重现性
   - 识别可能的异常数据点
   - 评估测量精度是否满足实验要求

3. **误差分析**
   - 分析系统误差和随机误差的来源
   - 评价不确定度计算的合理性
   - 提出减小误差的具体建议

4. **实验结论**
   - 总结实验的主要发现
   - 评价实验的完成质量
   - 给出改进建议

请用专业的光学实验语言进行分析，格式要清晰易读。
"""

        response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": "你是一位专业的光学实验教师，擅长薄透镜焦距测定实验的数据分析。请用专业、准确的语言进行分析。"},
                {"role": "user", "content": prompt}
            ],
            temperature=0.7,
            max_tokens=2000
        )

        return response.choices[0].message.content

    except Exception as e:
        print(f"AI分析失败: {e}")
        return f"AI分析失败：{str(e)}"

def prepare_lens_focal_length_for_db(data_from_frontend):
    """
    准备薄透镜焦距测定实验数据用于数据库存储
    """
    try:
        # 验证必要字段
        required_fields = ['student_id', 'student_name']
        for field in required_fields:
            if not data_from_frontend.get(field):
                raise ValueError(f"缺少必要字段: {field}")

        # 构建标准化的数据结构
        experiment_data = {
            'experiment_info': {
                'type': 'lens_focal_length',
                'title': '薄透镜焦距测定实验',
                'submission_timestamp_utc': datetime.utcnow().isoformat() + 'Z'
            },
            'student_info': {
                'id': data_from_frontend['student_id'],
                'name': data_from_frontend['student_name']
            },
            'convex_lens_data': data_from_frontend.get('convex_lens_data', {}),
            'concave_lens_data': data_from_frontend.get('concave_lens_data', {}),
            'uncertainty_analysis': data_from_frontend.get('uncertainty_analysis', {}),
            'precision_analysis': data_from_frontend.get('precision_analysis', {}),
            'analysis_results': {
                'ai_analysis': data_from_frontend.get('analysis_result'),
                'plot_data': data_from_frontend.get('plot_data'),
                'final_conclusions': data_from_frontend.get('final_conclusions', {})
            }
        }

        return experiment_data

    except Exception as e:
        raise ValueError(f"薄透镜焦距测定实验数据处理错误: {str(e)}")

def validate_lens_focal_length_data(data):
    """
    验证薄透镜焦距测定实验数据的有效性
    
    Args:
        data (dict): 实验数据
        
    Returns:
        tuple: (is_valid, error_message)
    """
    try:
        convex_data = data.get('convex_lens_data', {})
        concave_data = data.get('concave_lens_data', {})
        
        # 检查是否有至少一种测量方法的数据
        has_convex_data = any([
            convex_data.get('uv_method'),
            convex_data.get('autocollimation_method'),
            convex_data.get('conjugate_method')
        ])
        
        has_concave_data = any([
            concave_data.get('uv_method'),
            concave_data.get('autocollimation_method')
        ])
        
        if not has_convex_data and not has_concave_data:
            return False, "至少需要提供一种透镜的测量数据"
        
        # 检查焦距值的合理性
        for lens_type, lens_data in [('凸透镜', convex_data), ('凹透镜', concave_data)]:
            for method_name, method_data in lens_data.items():
                if method_data and 'mean_focal_length' in method_data:
                    focal_length = method_data['mean_focal_length']
                    if focal_length is not None:
                        if lens_type == '凸透镜' and focal_length <= 0:
                            return False, f"{lens_type}{method_name}的焦距必须为正数"
                        if lens_type == '凹透镜' and focal_length >= 0:
                            return False, f"{lens_type}{method_name}的焦距必须为负数"
        
        return True, "数据验证通过"
        
    except Exception as e:
        return False, f"数据验证失败: {str(e)}" 