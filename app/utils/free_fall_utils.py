"""
自由落体实验数据处理工具
"""
import numpy as np
from datetime import datetime

def prepare_free_fall_for_db(data_from_frontend):
    """
    将前端提交的自由落体实验数据转换为数据库存储格式
    
    Args:
        data_from_frontend (dict): 前端提交的原始数据
        
    Returns:
        dict: 格式化后的实验数据
    """
    try:
        # 提取基本信息
        student_id = data_from_frontend.get('student_id', '')
        student_name = data_from_frontend.get('student_name', '')
        heights = data_from_frontend.get('heights', [])
        times = data_from_frontend.get('times', [])
        gravity_values = data_from_frontend.get('gravity_values', [])
        plot_data = data_from_frontend.get('plot_data')
        analysis_result = data_from_frontend.get('analysis_result')
        
        # 验证必要数据
        if not student_id or not student_name:
            raise ValueError("学号和姓名不能为空")
        
        if not heights or not times or not gravity_values:
            raise ValueError("实验数据不完整")
        
        if len(heights) != len(times) or len(heights) != len(gravity_values):
            raise ValueError("数据长度不匹配")
        
        # 计算统计量
        avg_gravity = np.mean(gravity_values)
        std_gravity = np.std(gravity_values)
        relative_error = abs(avg_gravity - 9.8) / 9.8 * 100
        
        # 构建实验数据字典
        experiment_data = {
            'experiment_info': {
                'experiment_type': 'free_fall',
                'experiment_name': '自由落体实验',
                'student_id': student_id,
                'student_name': student_name,
                'submission_time': datetime.now().isoformat(),
                'description': '验证自由落体运动规律，测量当地重力加速度'
            },
            
            'raw_data': {
                'heights': heights,  # 高度数据 (m)
                'times': times,      # 时间数据 (s)
                'gravity_values': gravity_values  # 重力加速度值 (m/s²)
            },
            
            'calculated_results': {
                'average_gravity': float(avg_gravity),
                'std_gravity': float(std_gravity),
                'relative_error': float(relative_error),
                'measurement_count': len(heights),
                'height_range': {
                    'min': float(min(heights)),
                    'max': float(max(heights))
                },
                'time_range': {
                    'min': float(min(times)),
                    'max': float(max(times))
                }
            },
            
            'analysis_results': {
                'plot_data': plot_data,  # 图形数据（base64编码）
                'ai_analysis': analysis_result,  # AI分析结果
                'data_quality': {
                    'completeness': 'complete' if len(heights) >= 3 else 'incomplete',
                    'consistency': 'good' if std_gravity < 1.0 else 'needs_improvement',
                    'accuracy': 'excellent' if relative_error < 5 else 'good' if relative_error < 10 else 'needs_improvement'
                }
            },
            
            'experiment_metadata': {
                'equipment_used': ['自由落体实验仪', '光电门计时器', '钢球', '米尺'],
                'theoretical_value': 9.8,  # 标准重力加速度
                'units': {
                    'height': 'm',
                    'time': 's',
                    'gravity': 'm/s²'
                },
                'formula_used': 'g = 2h/t²',
                'error_sources': [
                    '空气阻力',
                    '仪器精度',
                    '释放时间误差',
                    '测量读数误差'
                ]
            }
        }
        
        return experiment_data
        
    except Exception as e:
        raise ValueError(f"数据处理失败: {str(e)}")

def validate_free_fall_data(data):
    """
    验证自由落体实验数据的有效性
    
    Args:
        data (dict): 实验数据
        
    Returns:
        tuple: (is_valid, error_message)
    """
    try:
        heights = data.get('heights', [])
        times = data.get('times', [])
        gravity_values = data.get('gravity_values', [])
        
        # 检查数据完整性
        if not heights or not times or not gravity_values:
            return False, "缺少必要的实验数据"
        
        if len(heights) != len(times) or len(heights) != len(gravity_values):
            return False, "数据长度不匹配"
        
        # 检查数据合理性
        for i, (h, t, g) in enumerate(zip(heights, times, gravity_values)):
            if h <= 0:
                return False, f"第{i+1}次测量的高度必须大于0"
            if t <= 0:
                return False, f"第{i+1}次测量的时间必须大于0"
            if g <= 0 or g > 20:  # 重力加速度应该在合理范围内
                return False, f"第{i+1}次计算的重力加速度值不合理: {g} m/s²"
        
        # 检查数据一致性
        calculated_g_values = [(2 * h) / (t * t) for h, t in zip(heights, times)]
        for i, (provided, calculated) in enumerate(zip(gravity_values, calculated_g_values)):
            if abs(provided - calculated) > 0.1:  # 允许0.1 m/s²的误差
                return False, f"第{i+1}次测量的重力加速度计算值与提供值不符"
        
        return True, "数据验证通过"
        
    except Exception as e:
        return False, f"数据验证失败: {str(e)}"

def calculate_free_fall_statistics(heights, times):
    """
    计算自由落体实验的统计量
    
    Args:
        heights (list): 高度数据
        times (list): 时间数据
        
    Returns:
        dict: 统计结果
    """
    try:
        # 计算重力加速度值
        gravity_values = [(2 * h) / (t * t) for h, t in zip(heights, times)]
        
        # 基本统计量
        avg_gravity = np.mean(gravity_values)
        std_gravity = np.std(gravity_values)
        relative_error = abs(avg_gravity - 9.8) / 9.8 * 100
        
        # 计算置信区间（95%置信水平，使用t分布）
        n = len(gravity_values)
        if n > 1:
            # 标准误差
            se = std_gravity / np.sqrt(n)
            # t分布的临界值（95%置信水平，自由度n-1）
            # 对于小样本，使用近似值
            t_critical = 2.0 if n <= 30 else 1.96  # 简化处理
            margin_of_error = t_critical * se
            confidence_interval = (avg_gravity - margin_of_error, avg_gravity + margin_of_error)
        else:
            confidence_interval = (avg_gravity, avg_gravity)
        
        return {
            'gravity_values': gravity_values,
            'average_gravity': float(avg_gravity),
            'std_gravity': float(std_gravity),
            'relative_error': float(relative_error),
            'confidence_interval': {
                'lower': float(confidence_interval[0]),
                'upper': float(confidence_interval[1])
            },
            'measurement_count': len(heights),
            'data_quality': {
                'completeness': 'complete' if len(heights) >= 3 else 'incomplete',
                'consistency': 'good' if std_gravity < 1.0 else 'needs_improvement',
                'accuracy': 'excellent' if relative_error < 5 else 'good' if relative_error < 10 else 'needs_improvement'
            }
        }
        
    except Exception as e:
        raise ValueError(f"统计计算失败: {str(e)}") 