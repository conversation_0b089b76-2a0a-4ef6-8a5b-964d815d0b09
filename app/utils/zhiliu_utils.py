# app/utils/zhiliu_utils.py
import numpy as np
import matplotlib.pyplot as plt
from scipy.interpolate import interp1d
import io
import base64
from .common_utils import get_openai_client_and_model, generate_experiment_plot_base
from datetime import datetime, timezone

def preprocess_data_zhiliu(k1_current, k01_current):
    """预处理制流电路实验数据 - 使用正确的理论计算公式"""
    ratios = np.arange(0, 1.1, 0.1)
    i0_k1 = k1_current[0]
    i0_k01 = k01_current[0]

    theoretical_k1 = [i0_k1 / (1 + 1 * ratio) if (1 + 1 * ratio) != 0 else float('inf') for ratio in ratios]
    theoretical_k01 = [i0_k01 / (1 + 10 * ratio) if (1 + 10 * ratio) != 0 else float('inf') for ratio in ratios]

    # Handle cases where theoretical is 0 to avoid division by zero
    relative_error_k1 = [
        ((actual - theo) / theo * 100) if theo != 0 else (float('inf') if actual != 0 else 0)
        for actual, theo in zip(k1_current, theoretical_k1)
    ]
    relative_error_k01 = [
        ((actual - theo) / theo * 100) if theo != 0 else (float('inf') if actual != 0 else 0)
        for actual, theo in zip(k01_current, theoretical_k01)
    ]

    return {
        "theoretical_k1": [round(x, 3) if x != float('inf') else 'inf' for x in theoretical_k1],
        "theoretical_k01": [round(x, 3) if x != float('inf') else 'inf' for x in theoretical_k01],
        "relative_error_k1": [round(x, 2) if x != float('inf') else 'inf' for x in relative_error_k1],
        "relative_error_k01": [round(x, 2) if x != float('inf') else 'inf' for x in relative_error_k01]
    }

def generate_zhiliu_plot(k1_current, k01_current):
    """生成制流电路实验图表，使用cubic插值绘制平滑曲线"""
    try:
        import matplotlib.pyplot as plt
        import numpy as np
        from scipy.interpolate import interp1d
        ratios = np.array([0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0])
        k1_current_np = np.array(k1_current, dtype=float)
        k01_current_np = np.array(k01_current, dtype=float)

        fig, ax = plt.subplots(figsize=(10, 6))
        ratios_smooth = np.linspace(0.0, 1.0, 100)

        if len(ratios) > 1 and len(k1_current_np) == len(ratios):
            f_k1 = interp1d(ratios, k1_current_np, kind='cubic', bounds_error=False, fill_value="extrapolate")
            k1_smooth = f_k1(ratios_smooth)
            ax.plot(ratios_smooth, k1_smooth, 'b-', label='k=1 (插值)', linewidth=2, alpha=0.8)
        ax.plot(ratios, k1_current_np, 'bo', markersize=6, markerfacecolor='white', markeredgecolor='blue', markeredgewidth=2, label='k=1 (实验点)')

        if len(ratios) > 1 and len(k01_current_np) == len(ratios):
            f_k01 = interp1d(ratios, k01_current_np, kind='cubic', bounds_error=False, fill_value="extrapolate")
            k01_smooth = f_k01(ratios_smooth)
            ax.plot(ratios_smooth, k01_smooth, 'r-', label='k=0.1 (插值)', linewidth=2, alpha=0.8)
        ax.plot(ratios, k01_current_np, 'ro', markersize=6, markerfacecolor='white', markeredgecolor='red', markeredgewidth=2, label='k=0.1 (实验点)')

        ax.set_xlabel('接入比例', fontsize=12)
        ax.set_ylabel('电流 (mA)', fontsize=12)
        ax.set_title('制流电路实验 - 电流与接入比例关系', fontsize=14)
        ax.grid(True, alpha=0.3)
        ax.legend(fontsize=11)
        ax.set_xlim(0, 1.0)
        if len(k1_current_np) > 0 and len(k01_current_np) > 0:
            ax.set_ylim(0, max(np.max(k1_current_np), np.max(k01_current_np)) * 1.15)
        elif len(k1_current_np) > 0:
            ax.set_ylim(0, np.max(k1_current_np) * 1.15)
        elif len(k01_current_np) > 0:
            ax.set_ylim(0, np.max(k01_current_np) * 1.15)
        else:
            ax.set_ylim(0, 1)

        # 使用统一工具保存
        plot_data = generate_experiment_plot_base(fig, dpi=150)
        return plot_data
    except Exception as e:
        import matplotlib.pyplot as plt
        plt.close('all')
        print(f"Error generating zhiliu plot: {e}")
        raise

def analyze_zhiliu_data_with_ai(k1_current, k01_current, processed_data, selected_model_id=None):
    """制流电路实验数据AI分析"""
    experiment_description = f"""
# 制流电路实验数据分析
## 实验条件
1. 实验使用滑线变阻器和电阻箱构建制流电路
2. k值定义为电阻箱阻值(Rz)与滑线变阻器总阻值(R0)的比值: k = Rz/R0
3. 接入比例定义为滑动端到绕线一端的长度与滑线变阻器总长度的比值: l/l0
4. 实验分别测量了k=1和k=0.1两种情况下，接入比例从0.0到1.0变化时的电流值
5. 为了比较调节范围和调节精度，在k=1时，路端电压设置为5福特，在k=0.1时，调节路端电压使接入比例为零时的电流与k=1时相等。

## 实验数据
接入比例从0.0到1.0，每次增加0.1:
k=1时的电流值(mA): {k1_current}
k=0.1时的电流值(mA): {k01_current}

## 预处理数据
理论计算k=1时的电流值(mA): {processed_data["theoretical_k1"]}
理论计算k=0.1时的电流值(mA): {processed_data["theoretical_k01"]}
k=1时的相对误差(%): {processed_data["relative_error_k1"]}
k=0.1时的相对误差(%): {processed_data["relative_error_k01"]}

## 需要分析的问题
1. 分析实验数据与理论计算的误差情况，指出可能的误差来源。
2. 判断存在以下误差：
    - a、如果接入比例为零时，电流不相等，提示需要将该电流调相等后重新测量。
    - b、该曲线起始下降速度较快，如果接入比例为0.1的点的相对误差较大为正值切较大，则可能是接入起始点选在了金属环的最外侧。
            应将金属环内测（与电阻丝相接点）设置为接入比例为的零点，以此计算接入比例。
    - c、如果某些点偏差较大，请具体指出这些点。
3. 给出实验正确与否的最终判断，是否需要重新测量。
"""
    try:
        client, model_name = get_openai_client_and_model(selected_model_id)
        analysis_response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": "请根据提供的实验数据和条件，分析实验结果与理论计算的误差情况，并给出实验是否需要重新测量的判断。"},
                {"role": "user", "content": experiment_description}
            ],
            temperature=0.2,
            max_tokens=2500
        )
        analysis = analysis_response.choices[0].message.content

        summary_prompt = f"""
需要对制流电路实验的分析结果进行总结，并生成数据表格。

以下是详细的分析结果：
{analysis}

请执行以下任务：
1. 生成一个简洁的表格，包含三列：
   第一列：接入比例(从0.0到1.0，每次增加0.1)
   第二列：k=1时的相对误差(%): {processed_data["relative_error_k1"]}
   第三列：k=0.1时的相对误差(%): {processed_data["relative_error_k01"]}
   - 表格应该简洁明了，使用Markdown格式
2. 提炼分析中的内容，只保留提示和警告，对于正确的内容无需输出，不要输出分析过程。警告使用红色字体。
3. 给出最终结论：实验是否正确，是否需要重新测量,如果全部误差都在+-15%内，实验通过。
   - 最终结论应该明确指出实验是否需要重做，如果要求重新做，则<font color='red'>重新做</font>，如果通过，则<font color='green'>通过</font>。
"""
        summary_response = client.chat.completions.create(
            model=model_name,
            messages=[
                {"role": "system", "content": "对制流电路实验的分析结果进行总结，并生成数据表格。直接输出，不要有多余的介绍。"},
                {"role": "user", "content": summary_prompt}
            ],
            temperature=0.2,
            max_tokens=2000
        )
        return summary_response.choices[0].message.content
    except Exception as e:
        print(f"Error in AI analysis for zhiliu: {e}")
        return f"AI分析过程中发生错误: {str(e)}"


def prepare_zhiliu_for_db(data_from_frontend):
    """
    Packages zhiliu experiment data into the standardized JSON structure for DB submission.
    """
    student_id = data_from_frontend.get('student_id')
    student_name = data_from_frontend.get('student_name')
    k1_current_str = data_from_frontend.get('k1_current', [])
    k01_current_str = data_from_frontend.get('k01_current', [])
    # plot_data_b64_from_frontend = data_from_frontend.get('plot_data') # Plot might be generated on server
    ai_analysis_text = data_from_frontend.get('analysis_result', "AI分析未执行或未提供。")
    selected_model_id = data_from_frontend.get('model_id', 'default') # Model used for analysis

    if not all([student_id, student_name]): # k1/k01 can be empty if user submits without data
        raise ValueError("学生ID或姓名缺失。")

    try:
        k1_current = [float(val) for val in k1_current_str]
        k01_current = [float(val) for val in k01_current_str]
    except ValueError:
        raise ValueError("电流数据格式无效，必须是数字。")

    # Generate plot if data is available
    plot_data_b64 = None
    if k1_current and k01_current: # Only generate plot if there's data
        try:
            plot_data_b64 = generate_zhiliu_plot(k1_current, k01_current)
        except Exception as e:
            print(f"Error generating plot during zhiliu data packaging: {e}")
            # Decide if this should be a fatal error or just log and continue
            # For now, we'll let it be None

    # Preprocess data for theoretical values (even if plot fails or no plot)
    processed_calcs = {}
    if k1_current and k01_current:
        try:
            processed_calcs = preprocess_data_zhiliu(k1_current, k01_current)
        except Exception as e:
            print(f"Error preprocessing zhiliu data: {e}")


    # Determine conclusion from AI text
    derived_conclusion = '不通过' # Default
    if isinstance(ai_analysis_text, str):
        if '实验通过' in ai_analysis_text or "<font color='green'>通过</font>" in ai_analysis_text:
            derived_conclusion = '通过'
        elif '重新做' in ai_analysis_text or '重新测量' in ai_analysis_text or \
             "<font color='red'>不通过</font>" in ai_analysis_text or \
             "<font color='red'>重新做</font>" in ai_analysis_text:
            derived_conclusion = '不通过'

    experiment_data_payload = {
            "raw_data":{
                "k1_current": k1_current,
                "k01_current": k01_current },
            "theoretical_data":{
                "theoretical_k1": processed_calcs.get("theoretical_k1"),
                "theoretical_k01": processed_calcs.get("theoretical_k01")},
            "relative_error":{
                "k1": processed_calcs.get("relative_error_k1"),
                "k01": processed_calcs.get("relative_error_k01")},
            "plot_image_base64": plot_data_b64,
            "model_id_used": selected_model_id,
            "analysis":{
                "text":ai_analysis_text,
                "conclusion":derived_conclusion}
    }
    return experiment_data_payload
