import matplotlib
matplotlib.use('Agg') # Should be at the very top

from flask import Flask, render_template, request, jsonify, redirect, url_for
import json
from datetime import datetime
import pymysql
from dotenv import load_dotenv
import traceback # For better error logging

# Custom modules
from utils.common_utils import setup_font
# Import packaging functions from utils
from utils.current_control_circuit_utils import prepare_zhiliu_for_db
from utils.oscilloscope_utils import prepare_oscilloscope_for_db
from utils.length_measurement_utils import prepare_changduceliang_for_db
from utils.spectrometer_utils import prepare_spectrometer_for_db  # 新增
from utils.gravity_measurement_photogate_utils import prepare_free_fall_for_db  # 新增自由落体实验
from utils.lens_focal_length_utils import prepare_lens_focal_length_for_db  # 新增薄透镜焦距测定实验
from routes.micro_length_lever_routes import micro_length_lever_bp

from routes.current_control_circuit_routes import current_control_circuit_bp
from routes.oscilloscope_routes import oscilloscope_bp
from routes.length_measurement_routes import length_measurement_bp
from routes.spectrometer_routes import spectrometer_bp  # 新增
from routes.gravity_measurement_photogate_routes import gravity_measurement_photogate_bp  # 新增光电控制法测量重力加速度实验
from routes.lens_focal_length_routes import lens_focal_length_bp  # 新增薄透镜焦距测定实验

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Initialize font
if not setup_font():
    app.logger.warning("Font setup failed or using fallback. Chinese characters might not display correctly in plots.")

# 在实验列表中添加分光计实验
@app.route('/')
def index():
    experiments_list = [
        {'id': 'current_control_circuit', 'name': '制流电路', 'description': '学习制流电路的工作原理，测量不同k值下的电流变化', 'url': url_for('experiment_page', exp_type='current_control_circuit')},
        {'id': 'oscilloscope', 'name': '示波器的使用', 'description': '掌握示波器和函数发生器的基本操作，学习信号测量和利萨茹图形', 'url': url_for('experiment_page', exp_type='oscilloscope')},
        {'id': 'length_measurement', 'name': '长度测量', 'description': '练习使用游标卡尺、螺旋测微计等精密测量工具，学习误差分析', 'url': url_for('experiment_page', exp_type='length_measurement')},
        {'id': 'spectrometer', 'name': '分光计的调节与使用', 'description': '学习分光计的调节与使用，测量三棱镜折射率和光栅常数', 'url': url_for('experiment_page', exp_type='spectrometer')},
        {'id': 'gravity_measurement_photogate', 'name': '光电控制法测量重力加速度', 'description': '使用光电门控制系统验证自由落体运动规律，测量当地重力加速度', 'url': url_for('experiment_page', exp_type='gravity_measurement_photogate')},
        {'id': 'density_measurement', 'name': '密度测量实验', 'description': '通过测量固体的质量和体积，计算其密度并分析误差', 'url': url_for('experiment_page', exp_type='density_measurement')},
        {'id': 'lens_focal_length', 'name': '薄透镜焦距测定实验', 'description': '使用物距像距法、自准法和共轭法测量凸透镜和凹透镜的焦距', 'url': url_for('experiment_page', exp_type='lens_focal_length')},
        {'id': 'micro_length_lever', 'name': '微小长度测量（光杠杆法）', 'description': '利用光杠杆法测量纸的厚度，体验微小长度的高精度测量', 'url': url_for('experiment_page', exp_type='micro_length_lever')},
    ]
    return render_template('index.html', experiments=experiments_list)

@app.route('/experiment/<exp_type>')
def experiment_page(exp_type):
    from utils.common_utils import model_registry  # 导入模型注册表
    available_models = model_registry.get_app_models()

    if exp_type == 'current_control_circuit':
        return render_template('experiments/current_control_circuit.html', available_models=available_models)
    elif exp_type == 'oscilloscope':
        return render_template('experiments/oscilloscope.html', available_models=available_models)
    elif exp_type == 'length_measurement':
        return render_template('experiments/length_measurement.html', available_models=available_models)
    elif exp_type == 'spectrometer':
        return render_template('experiments/spectrometer.html', available_models=available_models)
    elif exp_type == 'gravity_measurement_photogate':
        return render_template('experiments/gravity_measurement_photogate.html', available_models=available_models)
    elif exp_type == 'density_measurement':
        return render_template('experiments/density_measurement.html', available_models=available_models)
    elif exp_type == 'lens_focal_length':
        return render_template('experiments/lens_focal_length.html', available_models=available_models)
    elif exp_type == 'micro_length_lever':
        return render_template('experiments/micro_length_lever.html', available_models=available_models)
    else:
        return redirect(url_for('index'))

@app.route('/calculator')
def calculator_page():
    return render_template('calculator.html')

@app.route('/test-calculator')
def test_calculator_page():
    return render_template('test_calculator.html')

@app.route('/test-calculator-compact')
def test_calculator_compact_page():
    return render_template('test_calculator_compact.html')

@app.route('/experiment-test/<exp_type>')
def experiment_test_page(exp_type):
    """测试页面路由，用于测试JavaScript宏实现"""
    from utils.common_utils import model_registry  # 导入模型注册表
    available_models = model_registry.get_app_models()

    if exp_type == 'length_measurement':
        return render_template('experiments/length_measurement_test.html', available_models=available_models)
    else:
        return redirect(url_for('index'))

@app.route('/simple-test')
def simple_test_page():
    """简单测试页面，用于验证基本HTML结构"""
    return render_template('experiments/simple_test.html')

@app.route('/test-navigation')
def test_navigation_page():
    """测试导航组件中文显示"""
    return render_template('test_navigation.html')

# 注册所有蓝图
app.register_blueprint(current_control_circuit_bp)
app.register_blueprint(oscilloscope_bp)
app.register_blueprint(length_measurement_bp)
app.register_blueprint(spectrometer_bp)  # 新增注册分光计蓝图
app.register_blueprint(gravity_measurement_photogate_bp)  # 新增注册光电控制法测量重力加速度实验蓝图
app.register_blueprint(lens_focal_length_bp)  # 新增注册薄透镜焦距测定实验蓝图
app.register_blueprint(micro_length_lever_bp)

# --- Unified Database Saving Function (Simplified) ---
def save_experiment_to_database(student_id, experiment_type, experiment_data_json_payload):
    """
    Saves the fully packaged experiment data to the database.
    `experiment_data_json_payload` is a dictionary that will be serialized to JSON.
    """
    from utils.common_utils import experiment_db  # 导入数据库连接

    connection = None
    try:
        connection = experiment_db.get_connection()
        if not connection:
            app.logger.error("Failed to connect to the experiment database.")
            return {'success': False, 'message': '无法连接到实验数据库'}, 500

        with connection.cursor() as cursor:
            # Check if student exists
            sql_check_student = "SELECT id FROM students WHERE id = %s"
            cursor.execute(sql_check_student, (student_id,))
            student_record = cursor.fetchone()
            if not student_record:
                app.logger.warning(f"Student ID {student_id} not found in students table.")
                return {'success': False, 'message': f'学生ID {student_id} 不存在于学生信息库中，请先添加学生信息。'}, 404

            # Calculate new version
            sql_max_version = "SELECT MAX(version) as max_version FROM experiments WHERE student_id = %s AND experiment_type = %s"
            cursor.execute(sql_max_version, (student_id, experiment_type))
            result = cursor.fetchone()
            current_version = result['max_version'] if result and result['max_version'] is not None else 0
            new_version = current_version + 1

            # Default status for a new submission
            submission_status = 'submitted'

            sql_insert = """
            INSERT INTO experiments
            (student_id, experiment_type, experiment_data, version, submit_time, status)
            VALUES (%s, %s, %s, %s, %s, %s)
            """
            current_db_time = datetime.now()

            cursor.execute(sql_insert, (
                student_id,
                experiment_type,
                json.dumps(experiment_data_json_payload, ensure_ascii=False),
                new_version,
                current_db_time,
                submission_status
            ))
            last_id = cursor.lastrowid
        connection.commit()
        app.logger.info(f"Experiment data saved for student {student_id}, type {experiment_type}, record ID {last_id}, version {new_version}")
        return {
            'success': True,
            'message': '实验数据已成功提交',
            'record_id': last_id,
            'version': new_version
        }, 200

    except pymysql.MySQLError as db_err:
        app.logger.error(f"Database error during submission: {db_err}")
        if connection: connection.rollback()
        return {'success': False, 'message': f'数据库错误: {str(db_err)}'}, 500
    except Exception as e:
        app.logger.error(f"General error during submission: {e}", exc_info=True)
        if connection: connection.rollback()
        return {'success': False, 'message': f'保存实验数据时发生内部错误: {str(e)}'}, 500
    finally:
        if connection: connection.close()

# --- Main Submission Route ---
@app.route('/submit', methods=['POST'])
def submit_experiment_route():
    print("receive submit from front")
    try:
        data_from_frontend = request.get_json()
        if not data_from_frontend:
            return jsonify({'success': False, 'message': '请求数据为空'}), 400

        experiment_type = data_from_frontend.get('experiment_type')
        student_id = data_from_frontend.get('student_id')

        print(f"实验类型: {experiment_type}")
        print(f"学生ID: {student_id}")

        if not experiment_type or not student_id:
            return jsonify({'success': False, 'message': '提交信息不完整 (缺少学号或实验类型)'}), 400

        experiment_data_json_payload = None
        if experiment_type == 'zhiliu_circuit':
            experiment_data_json_payload = prepare_zhiliu_for_db(data_from_frontend)
        elif experiment_type == 'oscilloscope':
            experiment_data_json_payload = prepare_oscilloscope_for_db(data_from_frontend)
        elif experiment_type == 'changduceliang':
            experiment_data_json_payload = prepare_changduceliang_for_db(data_from_frontend)
        elif experiment_type == 'spectrometer':  # 新增分光计实验处理
            experiment_data_json_payload = prepare_spectrometer_for_db(data_from_frontend)
        elif experiment_type == 'free_fall':
            experiment_data_json_payload = prepare_free_fall_for_db(data_from_frontend)
        elif experiment_type == 'density_measurement':
            experiment_data_json_payload = prepare_changduceliang_for_db(data_from_frontend)
        elif experiment_type == 'lens_focal_length':  # 新增薄透镜焦距测定实验处理
            experiment_data_json_payload = prepare_lens_focal_length_for_db(data_from_frontend)
        elif experiment_type == 'micro_length_lever':
            experiment_data_json_payload = prepare_changduceliang_for_db(data_from_frontend)
        else:
            return jsonify({'success': False, 'message': f'不支持的实验类型: {experiment_type}'}), 400

        response_data, status_code = save_experiment_to_database(
            student_id,
            experiment_type,
            experiment_data_json_payload
        )
        return jsonify(response_data), status_code

    except ValueError as ve:
        app.logger.warning(f"Data packaging/validation error for submission: {ve}")
        return jsonify({'success': False, 'message': str(ve)}), 400
    except Exception as e:
        app.logger.error(f"全局提交接口错误: {e}", exc_info=True)
        traceback.print_exc()
        return jsonify({'success': False, 'message': f'提交过程中发生严重错误: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=7006, debug=True)