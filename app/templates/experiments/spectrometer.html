<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分光计的调节与使用实验</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/experiment.css') }}">
    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>

<body>
    <div id="app" class="container">
        <h1>分光计的调节与使用实验</h1>

        <!-- 步骤1: 实验原理 -->
        <div class="step-container active" id="step1">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">实验原理与目的</div>
            </div>
            <div class="instruction">
                <h3>实验目的</h3>
                <ul>
                    <li>学习分光计的调节与使用方法</li>
                    <li>掌握三棱镜最小偏向角法测量折射率</li>
                    <li>了解角度测量的基本原理和技术</li>
                    <li>培养精密光学仪器的操作技能</li>
                </ul>
                <h3>实验原理</h3>
                <p>分光计是一种精密的角度测量仪器，主要用于测量光线的偏向角、折射角等。</p>
                <p>三棱镜折射率的测量基于最小偏向角法，当入射角等于出射角时，偏向角达到最小值。</p>
            </div>
            <experiment-navigation-component
                :current-step="1"
                :show-prev="false"
                :show-next="true"
                next-text="开始实验"
                @next="nextStep">
            </experiment-navigation-component>
        </div>

        <!-- 步骤2: 仪器调节 -->
        <div class="step-container" id="step2">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">分光计的调节</div>
            </div>
            <div class="instruction">
                <h3>调节步骤</h3>
                <ol>
                    <li><strong>调节望远镜</strong>：使其能够观察到清晰的十字丝</li>
                    <li><strong>调节平行光管</strong>：使其发出平行光</li>
                    <li><strong>调节载物台</strong>：使其垂直于仪器主轴</li>
                </ol>
            </div>
            <experiment-navigation-component
                :current-step="2"
                :show-prev="true"
                :show-next="true"
                prev-text="上一步"
                next-text="下一步"
                @next="nextStep"
                @prev="prevStep">
            </experiment-navigation-component>
        </div>

        <!-- 步骤3: 测量三棱镜折射率 -->
        <div class="step-container" id="step3">
            <div class="step-header">
                <div class="step-number">3</div>
                <div class="step-title">测量三棱镜折射率</div>
            </div>
            <div class="instruction">
                <h3>测量步骤</h3>
                <ol>
                    <li>将三棱镜放在载物台上，使其一个面对准平行光管</li>
                    <li>调节入射角，寻找最小偏向角位置</li>
                    <li>记录最小偏向角和三棱镜顶角</li>
                    <li>根据公式计算折射率</li>
                </ol>
                <div class="data-input-section">
                    <h4>数据记录与计算</h4>
                    <table>
                        <thead>
                            <tr><th>测量项目</th><th>数值</th><th>单位</th></tr>
                        </thead>
                        <tbody>
                            <tr><td>三棱镜顶角 A</td><td><input type="number" step="0.01" id="apex-angle"></td><td>度</td></tr>
                            <tr><td>最小偏向角 δₘ</td><td><input type="number" step="0.01" id="min-deviation"></td><td>度</td></tr>
                            <tr><td>折射率 n</td><td><span id="refractive-index">-</span></td><td>-</td></tr>
                        </tbody>
                    </table>
                    <button onclick="calculateRefractiveIndex()">计算折射率</button>
                </div>
            </div>
            <experiment-navigation-component
                :current-step="3"
                :show-prev="true"
                :show-next="true"
                prev-text="上一步"
                next-text="完成实验"
                @next="finishExperiment"
                @prev="prevStep">
            </experiment-navigation-component>
        </div>

        <!-- 提交模态框 -->
        <experiment-submission-modal
            experiment-type="spectrometer"
            :visible="showSubmissionModal"
            @submit="handleSubmitExperiment"
            @cancel="handleCancelSubmission"
            @success="handleSubmissionSuccess"
            ref="submissionModal">
        </experiment-submission-modal>
    </div>

    <script src="{{ url_for('static', filename='js/experiment.js') }}"></script>
    <script src="{{ url_for('static', filename='js/vue-components.js') }}"></script>
    <script>
        const { createApp } = Vue;

        const app = createApp({
            components: {
                'experiment-navigation-component': ExperimentNavigationComponent,
                'experiment-submission-modal': ExperimentSubmissionModalComponent
            },
            data() {
                return { showSubmissionModal: false };
            },
            methods: {
                nextStep() { window.nextStep(); },
                prevStep() { window.prevStep(); },
                finishExperiment() { this.showSubmissionModal = true; },
                handleSubmitExperiment(submissionData) {
                    const experimentData = this.collectExperimentData();
                    this.submitExperiment(submissionData.studentId, submissionData.studentName, experimentData);
                },
                handleCancelSubmission() { this.showSubmissionModal = false; },
                handleSubmissionSuccess() {
                    this.showSubmissionModal = false;
                    alert('实验已成功完成并提交！');
                },
                collectExperimentData() {
                    return {
                        apexAngle: document.getElementById('apex-angle').value,
                        minDeviation: document.getElementById('min-deviation').value,
                        refractiveIndex: document.getElementById('refractive-index').textContent
                    };
                },
                async submitExperiment(studentId, studentName, experimentData) {
                    try {
                        this.$refs.submissionModal.showStatus('正在提交实验数据，请稍候...', '#e8f4fc', '#333');
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        this.$refs.submissionModal.showSuccess('恭喜！实验数据已成功提交。');
                    } catch (error) {
                        this.$refs.submissionModal.showError('提交失败：' + error.message);
                    }
                }
            }
        });

        app.mount('#app');

        function calculateRefractiveIndex() {
            const apexAngle = parseFloat(document.getElementById('apex-angle').value);
            const minDeviation = parseFloat(document.getElementById('min-deviation').value);

            if (apexAngle && minDeviation) {
                const apexRad = apexAngle * Math.PI / 180;
                const deviationRad = minDeviation * Math.PI / 180;

                const n = Math.sin((apexRad + deviationRad) / 2) / Math.sin(apexRad / 2);
                document.getElementById('refractive-index').textContent = n.toFixed(4);
            } else {
                alert('请输入三棱镜顶角和最小偏向角的数值！');
            }
        }
    </script>
</body>
</html>