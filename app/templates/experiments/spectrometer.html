{% set steps = steps %}
{% set custom_styles = custom_styles %}
{% set custom_scripts = custom_scripts %}
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分光计的调节与使用实验</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/experiment.css') }}">
    <!-- 添加中文字体支持 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;600&display=swap" rel="stylesheet">
    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        .header-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        .back-button {
            display: inline-block;
            padding: 8px 16px;
            border: 1px solid #ccc;
            background-color: #f0f0f0;
            color: #333;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
        }
        .back-button:hover {
            background-color: #e0e0e0;
        }
        .container h1 {
            margin: 0;
        }
    </style>
    {{ custom_styles|safe }}
</head>
<body>
    <div id="app" class="container">
        <div class="header-controls">
            <a href="javascript:history.back()" class="back-button">← 返回</a>
            <h1>分光计的调节与使用实验</h1>
            <div style="width: 80px;"></div>
        </div>

        {% for step in steps %}
            <div class="step-container {% if step.active %}active{% endif %}" id="step{{ step.number }}">
                <div class="step-header">
                    <div class="step-number">{{ step.number }}</div>
                    <div class="step-title">{{ step.title }}</div>
                </div>
                {% if step.type == 'with_substeps' and step.substeps %}
                    <div class="progress-bar">
                        <div class="progress-fill" id="step{{ step.number }}-progress" style="width: 0%"></div>
                    </div>
                    {% for substep in step.substeps %}
                        <div class="substep-container {% if substep.active %}active{% endif %}" id="step{{ step.number }}-substep{{ substep.number }}">
                            <div class="substep-header">
                                <div class="substep-number">{{ substep.number }}</div>
                                <div class="substep-title">{{ substep.title }}</div>
                            </div>
                            <div class="substep-content">
                                {{ substep.content|safe }}
                                <navigation-component
                                    :current-step="{{ step.number }}"
                                    :current-substep="{{ substep.number }}"
                                    :total-substeps="{{ step.substeps|length }}"
                                    :show-prev="{% if substep.number > 1 %}true{% else %}false{% endif %}"
                                    :show-next="true"
                                    prev-text="上一步"
                                    {% if substep.number < (step.substeps|length) %}
                                    next-text="下一步"
                                    {% else %}
                                    next-text="进入下一个实验步骤"
                                    :is-final-substep="true"
                                    {% endif %}
                                    @next="{% if substep.number < (step.substeps|length) %}nextSubStep{% else %}nextStep{% endif %}"
                                    @prev="prevSubStep">
                                </navigation-component>
                            </div>
                        </div>
                    {% endfor %}
                {% else %}
                    {{ step.content|safe }}
                    {% if step.buttons %}
                        <div class="btn-container">
                            {% for button in step.buttons %}
                                <button type="button"
                                    {% if button.onclick %}onclick="{{ button.onclick }}"{% endif %}
                                    {% if button.disabled %}disabled{% endif %}
                                    {% if button.class %}class="{{ button.class }}"{% endif %}
                                    {% if button.id %}id="{{ button.id }}"{% endif %}>
                                    {{ button.text }}
                                </button>
                            {% endfor %}
                        </div>
                    {% else %}
                        <navigation-component
                            :current-step="{{ step.number }}"
                            :current-substep="1"
                            :total-substeps="1"
                            :show-prev="{% if step.number > 1 %}true{% else %}false{% endif %}"
                            :show-next="true"
                            :is-step-start="true"
                            prev-text="上一步"
                            next-text="下一步"
                            @next="nextStep"
                            @prev="prevStep">
                        </navigation-component>
                    {% endif %}
                {% endif %}
            </div>
        {% endfor %}

        <div class="footer">
            © 2024 | 物理实验基础课程平台 | 网页设计：朱瑞华
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/showdown/2.1.0/showdown.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/showdown-katex@0.8.0/dist/showdown-katex.min.js"></script>
    <script src="{{ url_for('static', filename='js/experiment.js') }}"></script>
    <script>{{ custom_scripts|safe }}</script>

    <!-- 提交模态框 -->
    <div class="modal-overlay" id="submission-modal" style="display: none;">
        <div class="modal-content">
            <h3>提交实验结果</h3>
            <div class="modal-body">
                <div style="padding: 20px;">
                    <p style="margin-bottom: 20px; color: #666;">请输入您的学号和姓名以完成实验提交：</p>
                    <div style="margin: 15px 0;">
                        <label for="student-id" style="display: block; margin-bottom: 5px; font-weight: bold;">学号：</label>
                        <input type="text" id="student-id" placeholder="请输入学号"
                            style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>
                    <div style="margin: 15px 0;">
                        <label for="student-name" style="display: block; margin-bottom: 5px; font-weight: bold;">姓名：</label>
                        <input type="text" id="student-name" placeholder="请输入姓名"
                            style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                    </div>
                    <div id="submission-status" style="display: none; margin-top: 15px; padding: 10px; border-radius: 5px;"></div>
                </div>
            </div>
            <div class="modal-buttons">
                <button type="button" onclick="submitSpectrometerExperiment()">提交</button>
                <button type="button" onclick="closeSubmitModal()" class="prev-btn">取消</button>
            </div>
        </div>
    </div>
</body>
</html>
