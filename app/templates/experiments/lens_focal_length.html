{% from 'macros/experiment_macros.html' import render_experiment_layout, render_model_selector, render_modal, render_analysis_container, render_data_table %}

{% set steps = [
    {
        'number': 1,
        'title': '实验原理与方法简介',
        'type': 'with_substeps',
        'substeps': [
            {
                'number': 1,
                'title': '薄透镜成像公式与测量方法',
                'content': '''
<div class="instruction">
    <h3>薄透镜成像公式</h3>
    <div class="formula-box">
        <span class="katex">\frac{1}{f} = \frac{1}{u} + \frac{1}{v}</span>
        <p>其中 f 为焦距，u 为物距，v 为像距。</p>
    </div>
    <h4>实验目的</h4>
    <ul>
        <li>掌握薄透镜成像的基本原理</li>
        <li>学会使用不同方法测量凸透镜和凹透镜的焦距</li>
        <li>理解各种测量方法的优缺点和适用范围</li>
        <li>学会进行不确定度分析和数据处理</li>
    </ul>
    <h4>主要测量方法：</h4>
    <ul>
        <li><strong>物距像距法</strong>：适用于凸透镜、凹透镜，通过测量物距和像距计算焦距</li>
        <li><strong>自准法</strong>：适用于凸透镜、凹透镜，利用平面镜反射原理测量焦距</li>
        <li><strong>共轭法</strong>：仅适用于凸透镜，通过两次成像位置计算焦距</li>
    </ul>
    <h4>实验仪器</h4>
    <ul>
        <li>光学平台</li>
        <li>光源</li>
        <li>物屏（带十字刻线）</li>
        <li>凸透镜</li>
        <li>凹透镜</li>
        <li>光屏</li>
        <li>平面镜</li>
        <li>米尺</li>
    </ul>
    <h4>注意事项</h4>
    <ul>
        <li>保持光学元件同轴，避免像差</li>
        <li>调节时动作要轻，避免碰撞</li>
        <li>多次测量取平均值，减小随机误差</li>
        <li>记录数据时注意有效数字</li>
    </ul>
</div>
''',
                'active': True
            }
        ],
        'active': True
    },
    {
        'number': 2,
        'title': '凸透镜焦距测量',
        'type': 'with_substeps',
        'substeps': [
            {
                'number': 1,
                'title': '物距像距法',
                'content': '''
<div class="instruction">
    <h4>调节说明：</h4>
    <ul>
        <li>将光源、物屏、凸透镜、光屏依次放在光学平台上，保持同一直线。</li>
        <li>调节物距 u，使成像清晰，记录物距 u 和像距 v。</li>
        <li>多次改变物距，重复测量。</li>
    </ul>
</div>
<div class="data-input-section">
    <h4>数据记录表</h4>
    <table>
        <thead>
            <tr><th>序号</th><th>物距 u (cm)</th><th>像距 v (cm)</th><th>焦距 f (cm)</th></tr>
        </thead>
        <tbody>
            {% for i in range(1,6) %}
            <tr>
                <td>{{ i }}</td>
                <td><input type="number" step="0.01" class="convex-u"></td>
                <td><input type="number" step="0.01" class="convex-v"></td>
                <td><span class="convex-f">-</span></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <button onclick="calcConvexFocalLength1()">计算焦距</button>
</div>
<div class="uncertainty-container">
    <div class="formula-box">
        <span class="katex">f = \frac{uv}{u+v}</span>
    </div>
</div>
''',
                'active': True
            },
            {
                'number': 2,
                'title': '自准法',
                'content': '''
<div class="instruction">
    <h4>调节说明：</h4>
    <ul>
        <li>将光源、物屏、凸透镜、平面镜依次放置。</li>
        <li>调节物屏和透镜，使物像重合且清晰。</li>
        <li>记录物屏到透镜的距离，即为焦距。</li>
        <li>多次测量，取平均值。</li>
    </ul>
</div>
<div class="data-input-section">
    <h4>数据记录表</h4>
    <table>
        <thead>
            <tr><th>序号</th><th>物屏-透镜距离 f (cm)</th></tr>
        </thead>
        <tbody>
            {% for i in range(1,6) %}
            <tr>
                <td>{{ i }}</td>
                <td><input type="number" step="0.01" class="convex-autocollimation"></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <button onclick="calcConvexFocalLength2()">计算平均焦距</button>
</div>
<div class="uncertainty-container">
    <div class="formula-box">
        <span class="katex">f = \text{物屏-透镜距离}</span>
    </div>
</div>
''',
                'active': False
            },
            {
                'number': 3,
                'title': '共轭法',
                'content': '''
<div class="instruction">
    <h4>调节说明：</h4>
    <ul>
        <li>固定光源和光屏，移动凸透镜，分别找到两个成像清晰的位置，记录两次透镜位置 L1、L2。</li>
        <li>记录物屏与光屏的距离 L。</li>
        <li>多次测量，取平均值。</li>
    </ul>
</div>
<div class="data-input-section">
    <h4>数据记录表</h4>
    <table>
        <thead>
            <tr><th>序号</th><th>L (cm)</th><th>L1 (cm)</th><th>L2 (cm)</th><th>f (cm)</th></tr>
        </thead>
        <tbody>
            {% for i in range(1,4) %}
            <tr>
                <td>{{ i }}</td>
                <td><input type="number" step="0.01" class="conj-L"></td>
                <td><input type="number" step="0.01" class="conj-L1"></td>
                <td><input type="number" step="0.01" class="conj-L2"></td>
                <td><span class="conj-f">-</span></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <button onclick="calcConvexFocalLength3()">计算焦距</button>
</div>
<div class="uncertainty-container">
    <div class="formula-box">
        <span class="katex">f = \frac{L^2 - (L_2 - L_1)^2}{4L}</span>
    </div>
</div>
''',
                'active': False
            }
        ],
        'active': False
    },
    {
        'number': 3,
        'title': '凹透镜焦距测量',
        'type': 'with_substeps',
        'substeps': [
            {
                'number': 1,
                'title': '物距像距法',
                'content': '''
<div class="instruction">
    <h4>调节说明：</h4>
    <ul>
        <li>将凸透镜和凹透镜组合，先用凸透镜成实像，再将凹透镜放入，调节使成像清晰。</li>
        <li>记录物距 u'、像距 v'，利用公式计算凹透镜焦距。</li>
        <li>多次测量，取平均值。</li>
    </ul>
</div>
<div class="data-input-section">
    <h4>数据记录表</h4>
    <table>
        <thead>
            <tr><th>序号</th><th>物距 u' (cm)</th><th>像距 v' (cm)</th><th>焦距 f' (cm)</th></tr>
        </thead>
        <tbody>
            {% for i in range(1,5) %}
            <tr>
                <td>{{ i }}</td>
                <td><input type="number" step="0.01" class="concave-u"></td>
                <td><input type="number" step="0.01" class="concave-v"></td>
                <td><span class="concave-f">-</span></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <button onclick="calcConcaveFocalLength1()">计算焦距</button>
</div>
<div class="uncertainty-container">
    <div class="formula-box">
        <span class="katex">\frac{1}{f'} = \frac{1}{v'} - \frac{1}{u'}</span>
    </div>
</div>
''',
                'active': True
            },
            {
                'number': 2,
                'title': '自准法',
                'content': '''
<div class="instruction">
    <h4>调节说明：</h4>
    <ul>
        <li>将凹透镜与凸透镜组合，物屏、透镜组、平面镜依次放置。</li>
        <li>调节物屏和透镜组，使物像重合且清晰。</li>
        <li>记录物屏到透镜组的距离，结合凸透镜焦距，计算凹透镜焦距。</li>
        <li>多次测量，取平均值。</li>
    </ul>
</div>
<div class="data-input-section">
    <h4>数据记录表</h4>
    <table>
        <thead>
            <tr><th>序号</th><th>物屏-透镜组距离 d (cm)</th><th>计算焦距 f' (cm)</th></tr>
        </thead>
        <tbody>
            {% for i in range(1,5) %}
            <tr>
                <td>{{ i }}</td>
                <td><input type="number" step="0.01" class="concave-auto-d"></td>
                <td><span class="concave-auto-f">-</span></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    <button onclick="calcConcaveFocalLength2()">计算焦距</button>
</div>
<div class="uncertainty-container">
    <div class="formula-box">
        <span class="katex">f' = d - f_\text{convex}</span>
        <p>其中 d 为物屏-透镜组距离，f<sub>convex</sub> 为凸透镜焦距。</p>
    </div>
</div>
''',
                'active': False
            }
        ],
        'active': False
    },
    {
        'number': 4,
        'title': '结果与不确定度分析',
        'type': 'simple',
        'content': '''
<div class="analysis-container">
    <div class="analysis-content">
        <h4>数据汇总与不确定度提示</h4>
        <div class="table-container">
            <table>
                <thead>
                    <tr><th>方法</th><th>平均焦距 (cm)</th><th>标准差 (cm)</th></tr>
                </thead>
                <tbody>
                    <tr><td>凸透镜物距像距法</td><td id="convex-f-avg">-</td><td id="convex-f-std">-</td></tr>
                    <tr><td>凸透镜自准法</td><td id="convex-auto-f-avg">-</td><td id="convex-auto-f-std">-</td></tr>
                    <tr><td>凸透镜共轭法</td><td id="conj-f-avg">-</td><td id="conj-f-std">-</td></tr>
                    <tr><td>凹透镜物距像距法</td><td id="concave-f-avg">-</td><td id="concave-f-std">-</td></tr>
                    <tr><td>凹透镜自准法</td><td id="concave-auto-f-avg">-</td><td id="concave-auto-f-std">-</td></tr>
                </tbody>
            </table>
        </div>
        <div class="instruction">
            <ul>
                <li>不确定度可用多次测量的标准差估算。</li>
                <li>仪器分度误差可作为B类不确定度。</li>
                <li>合成不确定度：<span class="katex">u_C = \sqrt{u_A^2 + u_B^2}</span></li>
            </ul>
        </div>
    </div>
</div>
<div class="btn-container">
    <button class="prev-btn" onclick="prevStep(4)">上一步</button>
    <button onclick="finishExperiment()">完成实验</button>
</div>
''',
        'active': False
    }
] %}

{{ render_experiment_layout(
    title="薄透镜焦距测定实验",
    steps=steps,
    available_models=available_models
) }}

<script src="https://cdnjs.cloudflare.com/ajax/libs/mathjs/11.8.0/math.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/showdown@2.1.0/dist/showdown.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/showdown-katex@0.8.0/dist/showdown-katex.min.js"></script>
<script src="{{ url_for('static', filename='js/experiment.js') }}"></script>

</script>
</body>
</html> 