<!DOCTYPE html>
<html>
<head>
    <title>薄透镜焦距测定实验 - 交互式实验平台</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/experiment.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>

<body>
    <div id="app" class="container">
        <h1>薄透镜焦距测定实验</h1>

        <!-- 步骤1: 实验原理 -->
        <div class="step-container active" id="step1">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">实验原理与方法简介</div>
            </div>
            <div class="substep-container active" id="step1-substep1">
                <div class="instruction">
                    <h3>薄透镜成像公式</h3>
                    <div class="formula-box">
                        <span class="katex">\frac{1}{f} = \frac{1}{u} + \frac{1}{v}</span>
                        <p>其中 f 为焦距，u 为物距，v 为像距。</p>
                    </div>
                    <h4>实验目的</h4>
                    <ul>
                        <li>掌握薄透镜成像的基本原理</li>
                        <li>学会使用不同方法测量凸透镜和凹透镜的焦距</li>
                        <li>理解各种测量方法的优缺点和适用范围</li>
                    </ul>
                </div>
                <navigation-component
                    :current-step="1"
                    :current-substep="1"
                    :total-substeps="1"
                    :show-prev="false"
                    :show-next="true"
                    next-text="开始测量"
                    :is-final-substep="true"
                    @next="nextStep">
                </navigation-component>
            </div>
        </div>

        <!-- 步骤2: 凸透镜焦距测量 -->
        <div class="step-container" id="step2">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">凸透镜焦距测量</div>
            </div>
            <div class="substep-container active" id="step2-substep1">
                <div class="instruction">
                    <h4>物距像距法</h4>
                    <p>调节物距 u，使成像清晰，记录物距 u 和像距 v。</p>
                </div>
                <div class="data-input-section">
                    <table>
                        <thead>
                            <tr><th>序号</th><th>物距 u (cm)</th><th>像距 v (cm)</th><th>焦距 f (cm)</th></tr>
                        </thead>
                        <tbody>
                            <tr><td>1</td><td><input type="number" step="0.01" class="convex-u"></td><td><input type="number" step="0.01" class="convex-v"></td><td><span class="convex-f">-</span></td></tr>
                            <tr><td>2</td><td><input type="number" step="0.01" class="convex-u"></td><td><input type="number" step="0.01" class="convex-v"></td><td><span class="convex-f">-</span></td></tr>
                            <tr><td>3</td><td><input type="number" step="0.01" class="convex-u"></td><td><input type="number" step="0.01" class="convex-v"></td><td><span class="convex-f">-</span></td></tr>
                        </tbody>
                    </table>
                    <button onclick="calcConvexFocalLength1()">计算焦距</button>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="1"
                    :total-substeps="1"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="完成实验"
                    :is-step-start="true"
                    @next="finishExperiment"
                    @prev="prevStep">
                </navigation-component>
            </div>
        </div>

        <!-- 提交模态框 -->
        <experiment-submission-modal
            experiment-type="lens_focal_length"
            :visible="showSubmissionModal"
            @submit="handleSubmitExperiment"
            @cancel="handleCancelSubmission"
            @success="handleSubmissionSuccess"
            ref="submissionModal">
        </experiment-submission-modal>
    </div>

    <script src="{{ url_for('static', filename='js/experiment.js') }}"></script>
    <script src="{{ url_for('static', filename='js/vue-components.js') }}"></script>
    <script>
        const { createApp } = Vue;
        const NavigationComponent = {
            props: {
                currentStep: { type: Number, required: true },
                currentSubstep: { type: Number, default: 1 },
                totalSubsteps: { type: Number, default: 1 },
                showPrev: { type: Boolean, default: true },
                showNext: { type: Boolean, default: true },
                nextText: { type: String, default: '下一步' },
                prevText: { type: String, default: '上一步' },
                isFinalSubstep: { type: Boolean, default: false },
                isStepStart: { type: Boolean, default: false }
            },
            template: `
                <div class="substep-nav">
                    <button v-if="showPrev" @click="handlePrev" :class="prevButtonClass">{{ prevText }}</button>
                    <div v-else></div>
                    <button v-if="showNext" @click="handleNext">{{ nextText }}</button>
                    <div v-else></div>
                </div>
            `,
            computed: {
                prevButtonClass() { return this.isStepStart ? 'prev-btn' : ''; }
            },
            methods: {
                handlePrev() {
                    if (this.isStepStart) {
                        this.$emit('prev', this.currentStep);
                    } else {
                        this.$emit('prev', `step${this.currentStep}`, this.currentSubstep, this.totalSubsteps);
                    }
                },
                handleNext() {
                    if (this.isFinalSubstep || this.isStepStart) {
                        this.$emit('next', this.currentStep);
                    } else {
                        this.$emit('next', `step${this.currentStep}`, this.currentSubstep, this.totalSubsteps);
                    }
                }
            }
        };

        const app = createApp({
            components: {
                'navigation-component': NavigationComponent,
                'experiment-submission-modal': ExperimentSubmissionModalComponent
            },
            data() {
                return { showSubmissionModal: false };
            },
            methods: {
                nextStep(step) { window.nextStep(step); },
                prevStep(step) { window.prevStep(step); },
                finishExperiment() { this.showSubmissionModal = true; },
                handleSubmitExperiment(submissionData) {
                    this.submitExperiment(submissionData.studentId, submissionData.studentName, {});
                },
                handleCancelSubmission() { this.showSubmissionModal = false; },
                handleSubmissionSuccess() {
                    this.showSubmissionModal = false;
                    alert('实验已成功完成并提交！');
                },
                async submitExperiment(studentId, studentName, experimentData) {
                    try {
                        this.$refs.submissionModal.showStatus('正在提交实验数据，请稍候...', '#e8f4fc', '#333');
                        await new Promise(resolve => setTimeout(resolve, 1500));
                        this.$refs.submissionModal.showSuccess('恭喜！实验数据已成功提交。');
                    } catch (error) {
                        this.$refs.submissionModal.showError('提交失败：' + error.message);
                    }
                }
            }
        });
        app.mount('#app');

        function calcConvexFocalLength1() {
            const uInputs = document.querySelectorAll('.convex-u');
            const vInputs = document.querySelectorAll('.convex-v');
            const fSpans = document.querySelectorAll('.convex-f');

            for (let i = 0; i < uInputs.length; i++) {
                const u = parseFloat(uInputs[i].value);
                const v = parseFloat(vInputs[i].value);

                if (u && v && u > 0 && v > 0) {
                    const f = (u * v) / (u + v);
                    fSpans[i].textContent = f.toFixed(2);
                }
            }
        }
    </script>
</body>
</html>