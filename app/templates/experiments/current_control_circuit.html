<!-- ./app/templates/experiments/zhiliu_circuit.html -->
<!DOCTYPE html>
<html>
<head>
    <title>制流电路实验 - 交互式实验平台</title>
    <meta name="author" content="自动生成">
    <meta name="last-modified" content="2024/10/30">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/experiment.css') }}">
    <!-- 引入必要的库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        /* Vue导航组件样式 */
        .substep-nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 15px 0;
        }

        .substep-nav .nav-button {
            display: inline-block;
            margin: 15px 5px;
            padding: 12px 15px;
            background-color: #3498db;
            color: white !important;
            border: none;
            border-radius: 5px;
            font-size: 16px !important;
            cursor: pointer;
            transition: all 0.3s ease;
            width: auto;
            min-width: 100px;
            font-family: 'Noto Sans SC', 'Microsoft YaHei', 'SimHei', 'Segoe UI', Arial, sans-serif !important;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            font-weight: 400;
            line-height: 1.4;
        }

        .substep-nav .nav-button:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        }

        .substep-nav .nav-button.prev-btn {
            background-color: #95a5a6;
        }

        .substep-nav .nav-button.prev-btn:hover {
            background-color: #7f8c8d;
        }

        .validation-btn {
            background-color: #17a2b8 !important;
            color: white !important;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px !important;
            font-family: 'Noto Sans SC', 'Microsoft YaHei', 'SimHei', 'Segoe UI', Arial, sans-serif !important;
            font-weight: 400;
            line-height: 1.4;
        }

        .validation-btn:hover {
            background-color: #138496 !important;
        }

        .substep-nav .nav-button:disabled {
            background-color: #6c757d !important;
            cursor: not-allowed;
            color: white !important;
        }

        .substep-nav .nav-button:disabled:hover {
            background-color: #6c757d !important;
            transform: none;
            box-shadow: none;
        }
    </style>
</head>

<body>
    <div id="app" class="container">
        <h1>制流电路实验</h1>

        <!-- 步骤1: 实验器材准备与安全检查 -->
        <div class="step-container active" id="step1">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">实验器材准备与安全检查</div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="step1-progress" style="width: 0%"></div>
            </div>

            <div class="substep-container active" id="step1-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">准备晶体管直流稳压电源</div>
                </div>
                <div class="substep-content">
                    <p>请确认晶体管直流稳压电源是否可用，并检查以下事项：</p>
                    <ul>
                        <li>输出接线柱是否稳固</li>
                        <li>调压旋钮和调流旋钮是否灵活可控</li>
                    </ul>
                    <div class="instruction">
                        <strong>提示：</strong> 晶体管直流稳压电源将用于为制流电路提供稳定的电压。
                    </div>
                    <experiment-navigation-component
                        :current-step="1"
                        :current-substep="1"
                        :total-substeps="6"
                        :show-prev="false"
                        :show-next="true"
                        next-text="下一步"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step1-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">准备0.5级电流表</div>
                </div>
                <div class="substep-content">
                    <p>请检查电流表是否可用，并确认以下事项：</p>
                    <ul>
                        <li>表针是否位于零位</li>
                        <li>接线柱是否稳固</li>
                        <li>量程选择旋钮是否正常</li>
                    </ul>
                    <div class="instruction">
                        <strong>提示：</strong> 电流表将用于测量电路中的电流值，精度为0.5级。
                    </div>
                    <div class="warning">
                        <strong>注意事项：</strong>
                        <p>电流表应先设置在大量程，以防止初次通电时可能的大电流损坏电流表。</p>
                    </div>
                    <experiment-navigation-component
                        :current-step="1"
                        :current-substep="2"
                        :total-substeps="6"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="下一步"
                        @prev="handleSubstepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step1-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">准备电阻箱</div>
                </div>
                <div class="substep-content">
                    <p>请确认电阻箱是否可用，并检查以下事项：</p>
                    <ul>
                        <li>旋钮是否灵活可调</li>
                        <li>接线柱是否牢固</li>
                    </ul>
                    <div class="instruction">
                        <strong>提示：</strong>
                        <li>电阻箱将用于设置电路中的Rz值，它与滑线变阻器的总阻值R0的比值决定了k值(k=Rz/R0)。</li>
                    </div>
                    <div class="warning">
                        <strong>提示：</strong> 电阻箱的某个电阻烧毁外观无法判断，但相关挡位的电阻值会下降到1/10左右。
                    </div>
                    <experiment-navigation-component
                        :current-step="1"
                        :current-substep="3"
                        :total-substeps="6"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="下一步"
                        @prev="handleSubstepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step1-substep4">
                <div class="substep-header">
                    <div class="substep-number">4</div>
                    <div class="substep-title">准备滑线变阻器</div>
                </div>
                <div class="substep-content">
                    <p>请确认滑线变阻器是否可用，并检查以下事项：</p>
                    <ul>
                        <li>滑动接触是否平滑</li>
                        <li>滑动臂是否牢固</li>
                        <li>接线柱是否完好</li>
                    </ul>
                    <div class="instruction">
                        <strong>提示：</strong>
                        <li> 滑线变阻器的总阻值即R0，接入电阻RA和调整接入比例。</li>
                        <li> 接入比例是接入电阻RA与总阻值R0的比值，也等于绕线部分接入长度与总长度的比值。
                    </div>
                    <experiment-navigation-component
                        :current-step="1"
                        :current-substep="4"
                        :total-substeps="6"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="下一步"
                        @prev="handleSubstepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step1-substep5">
                <div class="substep-header">
                    <div class="substep-number">5</div>
                    <div class="substep-title">准备钮子开关</div>
                </div>
                <div class="substep-content">
                    <p>请确认钮子开关是否可用，并检查以下事项：</p>
                    <ul>
                        <li>接线柱是否牢固</li>
                    </ul>
                    <div class="instruction">
                        <strong>提示：</strong> 钮子开关将用于控制电路的通断，确保安全操作。
                    </div>
                    <experiment-navigation-component
                        :current-step="1"
                        :current-substep="5"
                        :total-substeps="6"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="下一步"
                        @prev="handleSubstepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step1-substep6">
                <div class="substep-header">
                    <div class="substep-number">6</div>
                    <div class="substep-title">准备连接导线</div>
                </div>
                <div class="substep-content">
                    <p>请确认连接导线是否齐全，并检查以下事项：</p>
                    <ul>
                        <li>导线接头是否清洁无氧化</li>
                        <li>导线长短合适、线数量足够</li>
                    </ul>
                    <div class="success-message" style="display: block;">
                        所有实验器材已准备就绪！您可以继续下一步实验。
                    </div>
                    <experiment-navigation-component
                        :current-step="1"
                        :current-substep="6"
                        :total-substeps="6"
                        :show-prev="true"
                        :show-next="true"
                        :is-final-substep="true"
                        prev-text="上一步"
                        next-text="进入下一个实验步骤"
                        @prev="handleSubstepPrev"
                        @next="handleStepNext">
                    </experiment-navigation-component>
                </div>
            </div>
        </div>

        <!-- 步骤2: 电路连接 -->
        <div class="step-container" id="step2">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">电路连接</div>
            </div>

            <div class="circuit-diagram">
                <img src="{{url_for('static', filename='images/figure1.png')}}" alt="制流电路详细连接图">
            </div>

            <div class="warning">
                <strong>安全检查：</strong>
                <ol>
                    <li>电源处于关闭状态</li>
                    <li>注意电流表正负极连接</li>
                    <li>处于断开状态</li>
                    <li>划线变阻器接触点置中部</li>
                    <li>导线排布是否整齐，无交叉短路风险</li>
                </ol>
            </div>

            <div class="success-message" style="display: block;">
                电路连接已完成并检查无误！您可以继续下一步实验。
            </div>

            <experiment-navigation-component
                :current-step="2"
                :show-prev="true"
                :show-next="true"
                :is-step-start="true"
                prev-text="上一步"
                next-text="下一步"
                @prev="handleStepPrev"
                @next="handleStepNext">
            </experiment-navigation-component>
        </div>

        <!-- 步骤3: k=1 测量准备 -->
        <div class="step-container" id="step3">
            <div class="step-header">
                <div class="step-number">3</div>
                <div class="step-title">k=1 测量准备</div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="step3-progress" style="width: 0%"></div>
            </div>

            <div class="substep-container active" id="step3-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">设置 k=1</div>
                </div>
                <div class="substep-content">
                    <p>现在我们需要设置电阻箱(Rz)与滑线变阻器(R0)的阻值，使 k=1：</p>

                    <div class="instruction">
                        <strong>实验参数说明：</strong>
                        <ul>
                            <li>K = Rz / R0：其中Rz为电阻箱设置的阻值，R0为滑线变阻器的总阻值</li>
                            <li>接入比例 = l/l0：其中l为滑动端到绕线一端的长度，l0为滑线变阻器的总长度</li>
                            <li>当滑动端与绕线的金属端接触时，接入比例为0</li>
                            <li>当滑动端与绕线的另一端接触时，接入比例为1</li>
                        </ul>
                    </div>

                    <div class="instruction">
                        <strong>设置步骤：</strong>
                        <ol>
                            <li>确定滑线变阻器(R0)的总阻值，例如100Ω</li>
                            <li>将电阻箱(Rz)的阻值调整为与滑线变阻器总阻值相等，即100Ω</li>
                            <li>这样确保 k = Rz/R0 = 1</li>
                        </ol>
                    </div>

                    <div class="highlight">
                        请调整电阻箱(Rz)，使其阻值等于滑线变阻器(R0)的总阻值。
                    </div>

                    <experiment-navigation-component
                        :current-step="3"
                        :current-substep="1"
                        :total-substeps="3"
                        :show-prev="true"
                        :show-next="true"
                        :is-step-start="true"
                        prev-text="返回上一步"
                        next-text="已设置，下一步"
                        @prev="handleStepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step3-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">设置电源电压</div>
                </div>
                <div class="substep-content">
                    <p>设置电源输出电压为5伏特：</p>

                    <div class="instruction">
                        <strong>设置步骤：</strong>
                        <ol>
                            <li>确保钮子开关仍处于断开状态</li>
                            <li>打开电源</li>
                            <li>缓慢调节电源调压旋钮，直到输出电压达到5伏特</li>
                            <li>读取电源面板上的电压表，确保电压稳定在5伏特</li>
                        </ol>
                    </div>

                    <div class="warning">
                        <strong>注意：</strong> 调节电压时应缓慢操作，避免电压突变。
                    </div>

                    <experiment-navigation-component
                        :current-step="3"
                        :current-substep="2"
                        :total-substeps="3"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="已设置，下一步"
                        @prev="handleSubstepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step3-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">电流表量程确认</div>
                </div>
                <div class="substep-content">
                    <p>确认电流表量程设置在安全范围：</p>

                    <div class="instruction">
                        <strong>确认步骤：</strong>
                        <ol>
                            <li>再次确认电流表设置在较大量程(例如100mA)</li>
                            <li>闭合电键，逐渐调小电流表的量程，观察电流表指针偏转至较大值</li>
                            <li>准备记录表格，用于记录不同接入比例下的电流值</li>
                            <li>确保有足够的空间记录11个不同接入比例下的测量值(0.0到1.0)</li>
                        </ol>
                    </div>

                    <div class="warning">
                        <strong>提示：</strong>
                        在打开电源前，必须确认电流表量程已设置为较大值，以防电流过大损坏电流表。测量过程中可根据实际电流大小调整到合适量程。如果电流表指针始终不偏转，则检查电路连接问题。
                    </div>

                    <div class="success-message" style="display: block;">
                        k=1 测量准备工作已完成！您可以继续下一步实验。
                    </div>

                    <experiment-navigation-component
                        :current-step="3"
                        :current-substep="3"
                        :total-substeps="3"
                        :show-prev="true"
                        :show-next="true"
                        :is-final-substep="true"
                        prev-text="上一步"
                        next-text="进入下一个实验步骤"
                        @prev="handleSubstepPrev"
                        @next="handleStepNext">
                    </experiment-navigation-component>
                </div>
            </div>
        </div>

        <!-- 步骤4: k=1 数据测量 -->
        <div class="step-container" id="step4">
            <div class="step-header">
                <div class="step-number">4</div>
                <div class="step-title">k=1 数据测量</div>
            </div>

            <p>现在开始测量 k=1 时不同接入比例下的电流值：</p>

            <div class="instruction">
                <strong>测量步骤：</strong>
                <ol>
                    <li>保持钮子开关闭合</li>
                    <li>调整滑线变阻器从接入比例。从0.0开始，逐步调整滑线变阻器的滑动端位置</li>
                    <li>每调整一次位置，记录对应的电流值</li>
                    <li>完成所有测量后，断开钮子开关</li>
                </ol>
            </div>

            <div class="warning">
                <ul>
                    <li>接入比例一定从0.0开始测量</li>
                </ul>
            </div>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>接入比例</th>
                            <th>k=1时的电流</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>1</td><td>0.0</td><td><input type="number" step="0.01" class="k1-current" required></td></tr>
                        <tr><td>2</td><td>0.1</td><td><input type="number" step="0.01" class="k1-current" required></td></tr>
                        <tr><td>3</td><td>0.2</td><td><input type="number" step="0.01" class="k1-current" required></td></tr>
                        <tr><td>4</td><td>0.3</td><td><input type="number" step="0.01" class="k1-current" required></td></tr>
                        <tr><td>5</td><td>0.4</td><td><input type="number" step="0.01" class="k1-current" required></td></tr>
                        <tr><td>6</td><td>0.5</td><td><input type="number" step="0.01" class="k1-current" required></td></tr>
                        <tr><td>7</td><td>0.6</td><td><input type="number" step="0.01" class="k1-current" required></td></tr>
                        <tr><td>8</td><td>0.7</td><td><input type="number" step="0.01" class="k1-current" required></td></tr>
                        <tr><td>9</td><td>0.8</td><td><input type="number" step="0.01" class="k1-current" required></td></tr>
                        <tr><td>10</td><td>0.9</td><td><input type="number" step="0.01" class="k1-current" required></td></tr>
                        <tr><td>11</td><td>1.0</td><td><input type="number" step="0.01" class="k1-current" required></td></tr>
                    </tbody>
                </table>
            </div>

            <experiment-navigation-component
                :current-step="4"
                :show-prev="true"
                :show-next="true"
                :is-step-start="true"
                :has-validation="true"
                prev-text="上一步"
                next-text="下一步"
                next-button-id="next-btn-4"
                validation-function="checkK1Data"
                @prev="handleStepPrev"
                @next="handleStepNext"
                @validate="handleValidation">
            </experiment-navigation-component>
        </div>

        <!-- 步骤5: k=0.1 测量准备 -->
        <div class="step-container" id="step5">
            <div class="step-header">
                <div class="step-number">5</div>
                <div class="step-title">k=0.1 测量准备</div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="step5-progress" style="width: 0%"></div>
            </div>

            <div class="substep-container active" id="step5-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">断开电路</div>
                </div>
                <div class="substep-content">
                    <p>在进行新的测量前，需要断开电路：</p>
                    <div class="instruction">
                        <strong>操作步骤：</strong>
                        <ol>
                            <li>断开钮子开关</li>
                            <li>确认电路中无电流流过</li>
                        </ol>
                    </div>
                    <experiment-navigation-component
                        :current-step="5"
                        :current-substep="1"
                        :total-substeps="7"
                        :show-prev="true"
                        :show-next="true"
                        :is-step-start="true"
                        prev-text="返回上一步"
                        next-text="已断开，下一步"
                        @prev="handleStepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step5-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">调节电源电压为零</div>
                </div>
                <div class="substep-content">
                    <p>将电源输出电压调节为零：</p>
                    <div class="instruction">
                        <strong>操作步骤：</strong>
                        <ol>
                            <li>逆时针旋转电源调压旋钮</li>
                            <li>直到电源面板上的电压表显示为零</li>
                        </ol>
                    </div>
                    <experiment-navigation-component
                        :current-step="5"
                        :current-substep="2"
                        :total-substeps="7"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="已调节，下一步"
                        @prev="handleSubstepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step5-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">设置 k=0.1</div>
                </div>
                <div class="substep-content">
                    <p>设置电阻箱(Rz)的阻值，使 k=0.1：</p>
                    <div class="instruction">
                        <strong>设置步骤：</strong>
                        <ol>
                            <li>计算 Rz = 0.1 × R0，其中 R0 是滑线变阻器的总阻值</li>
                            <li>例如：如果 R0 = 100Ω，则 Rz = 10Ω</li>
                            <li>将电阻箱调整到计算出的阻值</li>
                        </ol>
                    </div>
                    <experiment-navigation-component
                        :current-step="5"
                        :current-substep="3"
                        :total-substeps="7"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="已设置，下一步"
                        @prev="handleSubstepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step5-substep4">
                <div class="substep-header">
                    <div class="substep-number">4</div>
                    <div class="substep-title">调整滑线变阻器初始位置</div>
                </div>
                <div class="substep-content">
                    <p>将滑线变阻器滑动端置于接入比例为零的位置：</p>
                    <div class="instruction">
                        <strong>操作步骤：</strong>
                        <ol>
                            <li>移动滑线变阻器的滑动端</li>
                            <li>将滑动端与绕线的金属端接触</li>
                            <li>此时接入比例为零</li>
                        </ol>
                    </div>
                    <experiment-navigation-component
                        :current-step="5"
                        :current-substep="4"
                        :total-substeps="7"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="已调整，下一步"
                        @prev="handleSubstepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step5-substep5">
                <div class="substep-header">
                    <div class="substep-number">5</div>
                    <div class="substep-title">闭合钮子开关</div>
                </div>
                <div class="substep-content">
                    <p>准备开始测量，闭合钮子开关：</p>
                    <div class="instruction">
                        <strong>操作步骤：</strong>
                        <ol>
                            <li>确认电源电压仍为零</li>
                            <li>闭合钮子开关</li>
                            <li>电路此时已连通，但因为电压为零，所以无电流流过</li>
                        </ol>
                    </div>
                    <experiment-navigation-component
                        :current-step="5"
                        :current-substep="5"
                        :total-substeps="7"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="已闭合，下一步"
                        @prev="handleSubstepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step5-substep6">
                <div class="substep-header">
                    <div class="substep-number">6</div>
                    <div class="substep-title">调节电源电压</div>
                </div>
                <div class="substep-content">
                    <p>调节电源电压，使电流值等于 k=1 时的初始电流值：</p>
                    <div class="instruction">
                        <strong>操作步骤：</strong>
                        <ol>
                            <li>缓慢顺时针旋转电源调压旋钮，增加输出电压</li>
                            <li>观察电流表读数</li>
                            <li>当电流值达到 k=1 时接入比例为 0.0 的电流值时，停止调节</li>
                        </ol>
                    </div>
                    <experiment-navigation-component
                        :current-step="5"
                        :current-substep="6"
                        :total-substeps="7"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="已调节，下一步"
                        @prev="handleSubstepPrev"
                        @next="handleSubstepNext">
                    </experiment-navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step5-substep7">
                <div class="substep-header">
                    <div class="substep-number">7</div>
                    <div class="substep-title">记录初始电流值</div>
                </div>
                <div class="substep-content">
                    <p>记录 k=0.1 时接入比例为 0.0 的电流值：</p>
                    <div class="success-message" style="display: block;">
                        k=0.1 测量准备工作已完成！您可以继续下一步实验。
                    </div>
                    <experiment-navigation-component
                        :current-step="5"
                        :current-substep="7"
                        :total-substeps="7"
                        :show-prev="true"
                        :show-next="true"
                        :is-final-substep="true"
                        prev-text="上一步"
                        next-text="进入下一个实验步骤"
                        @prev="handleSubstepPrev"
                        @next="handleStepNext">
                    </experiment-navigation-component>
                </div>
            </div>
        </div>

        <!-- 步骤6: k=0.1 数据测量 -->
        <div class="step-container" id="step6">
            <div class="step-header">
                <div class="step-number">6</div>
                <div class="step-title">k=0.1 数据测量</div>
            </div>

            <p>现在开始测量 k=0.1 时不同接入比例下的电流值：</p>

            <div class="table-container">
                <table>
                    <thead>
                        <tr>
                            <th>序号</th>
                            <th>接入比例</th>
                            <th>k=0.1时的电流</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td>1</td><td>0.0</td><td><input type="number" step="0.01" class="k01-current" required></td></tr>
                        <tr><td>2</td><td>0.1</td><td><input type="number" step="0.01" class="k01-current" required></td></tr>
                        <tr><td>3</td><td>0.2</td><td><input type="number" step="0.01" class="k01-current" required></td></tr>
                        <tr><td>4</td><td>0.3</td><td><input type="number" step="0.01" class="k01-current" required></td></tr>
                        <tr><td>5</td><td>0.4</td><td><input type="number" step="0.01" class="k01-current" required></td></tr>
                        <tr><td>6</td><td>0.5</td><td><input type="number" step="0.01" class="k01-current" required></td></tr>
                        <tr><td>7</td><td>0.6</td><td><input type="number" step="0.01" class="k01-current" required></td></tr>
                        <tr><td>8</td><td>0.7</td><td><input type="number" step="0.01" class="k01-current" required></td></tr>
                        <tr><td>9</td><td>0.8</td><td><input type="number" step="0.01" class="k01-current" required></td></tr>
                        <tr><td>10</td><td>0.9</td><td><input type="number" step="0.01" class="k01-current" required></td></tr>
                        <tr><td>11</td><td>1.0</td><td><input type="number" step="0.01" class="k01-current" required></td></tr>
                    </tbody>
                </table>
            </div>

            <experiment-navigation-component
                :current-step="6"
                :show-prev="true"
                :show-next="true"
                :is-step-start="true"
                :has-validation="true"
                prev-text="上一步"
                next-text="下一步"
                next-button-id="next-btn-6"
                validation-function="checkK01Data"
                @prev="handleStepPrev"
                @next="handleStepNext"
                @validate="handleValidation">
            </experiment-navigation-component>
        </div>

        <!-- 步骤7: 数据分析与图形生成 -->
        <div class="step-container" id="step7">
            <div class="step-header">
                <div class="step-number">7</div>
                <div class="step-title">数据分析与图形生成</div>
            </div>

            <p>根据测量的数据，我们可以绘制电流与接入比例的关系图：</p>

            <!-- AI模型选择器 (替代原来的宏) -->
            <div class="model-selector-container">
                <label for="model-selector">AI模型选择：</label>
                <select id="model-selector" class="model-selector">
                    <option value="gpt-4">GPT-4</option>
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    <option value="claude-3">Claude-3</option>
                </select>
            </div>

            <button onclick="generatePlot()">生成图形</button>
            <button onclick="analyzeData()" style="background-color: #27ae60; margin-left: 10px;">AI 分析数据</button>

            <div class="table-container" id="data-table-container" style="margin-top: 20px; display: none;">
                <h3>实验数据汇总</h3>
                <table>
                    <thead>
                        <tr>
                            <th>接入比例</th>
                            <th>k=1时的电流</th>
                            <th>k=0.1时的电流</th>
                        </tr>
                    </thead>
                    <tbody id="results-body">
                        <!-- 表格内容将由JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>

            <div id="plot-container">
                <img id="plot-image" style="display: none;">
            </div>

            <!-- 分析容器 (替代原来的宏) -->
            <div id="analysis-container" class="analysis-container" style="display: none;">
                <h3>AI 分析结果</h3>
                <div id="analysis-content" class="analysis-content">
                    <!-- AI分析结果将显示在这里 -->
                </div>
            </div>

            <experiment-navigation-component
                :current-step="7"
                :show-prev="true"
                :show-next="true"
                :is-step-start="true"
                prev-text="上一步"
                next-text="完成实验"
                @prev="handleStepPrev"
                @next="handleFinishExperiment">
            </experiment-navigation-component>
        </div>

        <div class="footer">
            © 2024 | 物理实验基础课程平台 | 网页设计：朱瑞华
        </div>
    </div>

    <!-- 提交模态框 -->
    <experiment-submission-modal
        experiment-type="current_control_circuit"
        :visible="showSubmissionModal"
        @submit="handleSubmitExperiment"
        @cancel="handleCancelSubmission"
        @success="handleSubmissionSuccess"
        ref="submissionModal">
    </experiment-submission-modal>

    <!-- 引入必要的库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/showdown/2.1.0/showdown.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/showdown-katex@0.8.0/dist/showdown-katex.min.js"></script>

    <!-- 引入通用实验脚本 -->
    <script src="{{ url_for('static', filename='js/experiment.js') }}"></script>
    <!-- 引入Vue组件 -->
    <script src="{{ url_for('static', filename='js/vue-components.js') }}"></script>

    <!-- Vue应用和制流电路实验特有脚本 -->
    <script>
        // Vue应用初始化
        const { createApp } = Vue;

        const app = createApp({
            data() {
                return {
                    currentStep: 1,
                    currentSubstep: 1,
                    showSubmissionModal: false
                }
            },
            components: {
                'experiment-navigation-component': ExperimentNavigationComponent,
                'experiment-submission-modal': ExperimentSubmissionModalComponent
            },
            methods: {
                handleSubstepNext(stepId, substep, totalSubsteps) {
                    if (typeof stepId === 'string') {
                        nextSubStep(stepId, substep, totalSubsteps);
                    } else {
                        // 处理最后一个子步骤的情况
                        nextSubStep(`step${this.currentStep}`, this.currentSubstep, totalSubsteps);
                    }
                },
                handleSubstepPrev(stepId, substep, totalSubsteps) {
                    if (typeof stepId === 'string') {
                        prevSubStep(stepId, substep, totalSubsteps);
                    } else {
                        prevSubStep(`step${this.currentStep}`, this.currentSubstep, totalSubsteps);
                    }
                },
                handleStepNext(step) {
                    if (typeof step === 'number') {
                        nextStep(step);
                        this.currentStep = step + 1;
                    } else {
                        nextStep(this.currentStep);
                        this.currentStep = this.currentStep + 1;
                    }
                },
                handleStepPrev(step) {
                    if (typeof step === 'number') {
                        prevStep(step);
                        this.currentStep = step - 1;
                    } else {
                        prevStep(this.currentStep);
                        this.currentStep = this.currentStep - 1;
                    }
                },
                handleValidation(validationFunction) {
                    if (validationFunction === 'checkK1Data') {
                        checkK1Data();
                    } else if (validationFunction === 'checkK01Data') {
                        checkK01Data();
                    }
                },
                handleFinishExperiment() {
                    this.showSubmissionModal = true;
                },
                handleSubmitExperiment(submissionData) {
                    const experimentData = this.collectExperimentData();
                    this.submitExperiment(submissionData.studentId, submissionData.studentName, experimentData);
                },
                handleCancelSubmission() {
                    this.showSubmissionModal = false;
                },
                handleSubmissionSuccess() {
                    this.showSubmissionModal = false;
                    alert('实验已成功完成并提交！');
                },
                collectExperimentData() {
                    // 收集K1电流数据
                    const k1_current = [];
                    document.querySelectorAll('.k1-current').forEach(input => {
                        if (input.value) k1_current.push(parseFloat(input.value));
                    });

                    // 收集K01电流数据
                    const k01_current = [];
                    document.querySelectorAll('.k01-current').forEach(input => {
                        if (input.value) k01_current.push(parseFloat(input.value));
                    });

                    // 收集绘图数据
                    let plotData = null;
                    const plotContainer = document.getElementById('plot-container');
                    if (plotContainer && plotContainer.style.display !== 'none') {
                        plotData = 'Plot data available';
                    }

                    // 收集分析结果
                    let analysisResult = null;
                    const analysisContent = document.getElementById('analysis-content');
                    if (analysisContent && analysisContent.innerHTML &&
                        document.getElementById('analysis-container').style.display !== 'none') {
                        analysisResult = analysisContent.innerText || analysisContent.textContent;
                    }

                    return {
                        k1_current: k1_current,
                        k01_current: k01_current,
                        plot_data: plotData,
                        analysis_result: analysisResult
                    };
                },
                async submitExperiment(studentId, studentName, experimentData) {
                    try {
                        this.$refs.submissionModal.showStatus('正在提交实验数据，请稍候...', '#e8f4fc', '#333');

                        // 准备提交数据
                        const submissionData = {
                            student_id: studentId,
                            student_name: studentName,
                            ...experimentData
                        };

                        // 使用通用提交处理函数
                        await new Promise((resolve, reject) => {
                            handleExperimentSubmission('current_control_circuit', submissionData,
                                (result) => {
                                    this.$refs.submissionModal.showSuccess(`恭喜！实验数据已成功提交。记录ID: ${result.record_id}`);
                                    resolve(result);
                                },
                                (error) => {
                                    this.$refs.submissionModal.showError('提交失败：' + error);
                                    reject(new Error(error));
                                }
                            );
                        });
                    } catch (error) {
                        this.$refs.submissionModal.showError('提交失败：' + error.message);
                    }
                }
            }
        });

        app.mount('#app');

        // 检查 k=1 的数据是否填写完整
        function checkK1Data() {
            const inputs = document.getElementsByClassName('k1-current');
            let allFilled = true;

            for (let input of inputs) {
                if (!input.value || isNaN(parseFloat(input.value))) {
                    allFilled = false;
                    break;
                }
            }

            if (allFilled) {
                alert('数据验证通过！您可以继续下一步。');
                const nextBtn = document.getElementById('next-btn-4');
                if (nextBtn) nextBtn.disabled = false;
            } else {
                alert('请填写所有的 k=1 电流数据！');
            }
        }

        // 检查 k=0.1 的数据是否填写完整
        function checkK01Data() {
            const inputs = document.getElementsByClassName('k01-current');
            let allFilled = true;

            for (let input of inputs) {
                if (!input.value || isNaN(parseFloat(input.value))) {
                    allFilled = false;
                    break;
                }
            }

            if (allFilled) {
                alert('数据验证通过！您可以继续下一步。');
                const nextBtn = document.getElementById('next-btn-6');
                if (nextBtn) nextBtn.disabled = false;
            } else {
                alert('请填写所有的 k=0.1 电流数据！');
            }
        }

        function generatePlot() {
            const k1_inputs = document.getElementsByClassName('k1-current');
            const k01_inputs = document.getElementsByClassName('k01-current');

            const k1_current = Array.from(k1_inputs).map(input => parseFloat(input.value));
            const k01_current = Array.from(k01_inputs).map(input => parseFloat(input.value));

            // 检查数据是否完整
            if (k1_current.some(isNaN) || k01_current.some(isNaN)) {
                alert('请确保所有数据都已填写！');
                return;
            }

            // 创建数据表格
            const resultsBody = document.getElementById('results-body');
            resultsBody.innerHTML = ''; // 清空表格

            const ratios = [0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0];

            for (let i = 0; i < ratios.length; i++) {
                const row = document.createElement('tr');

                // 接入比例
                const ratioCell = document.createElement('td');
                ratioCell.textContent = ratios[i].toFixed(1);
                row.appendChild(ratioCell);

                // k=1时的电流
                const k1Cell = document.createElement('td');
                k1Cell.textContent = k1_current[i].toFixed(2);
                row.appendChild(k1Cell);

                // k=0.1时的电流
                const k01Cell = document.createElement('td');
                k01Cell.textContent = k01_current[i].toFixed(2);
                row.appendChild(k01Cell);

                resultsBody.appendChild(row);
            }

            // 显示数据表格容器
            document.getElementById('data-table-container').style.display = 'block';

            // 发送数据到服务器生成图形
            fetch('/current_control_circuit/plot', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    k1_current: k1_current,
                    k01_current: k01_current
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                const plotImage = document.getElementById('plot-image');
                plotImage.src = 'data:image/png;base64,' + data.plot_url;
                plotImage.style.display = 'block';

                // 滚动到图形位置
                document.getElementById('plot-container').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            })
            .catch(error => {
                console.error('Error:', error);
                alert('生成图形时发生错误：' + error.message);
            });
        }

        function analyzeData() {
            const k1_inputs = document.getElementsByClassName('k1-current');
            const k01_inputs = document.getElementsByClassName('k01-current');

            const k1_current = Array.from(k1_inputs).map(input => parseFloat(input.value));
            const k01_current = Array.from(k01_inputs).map(input => parseFloat(input.value));

            // 检查数据是否完整
            if (k1_current.some(isNaN) || k01_current.some(isNaN)) {
                alert('请确保所有数据都已填写！');
                return;
            }

            // 获取选择的模型ID
            const selectedModelId = document.getElementById('model-selector').value;

            // 显示分析容器和加载状态
            const analysisContainer = document.getElementById('analysis-container');
            const analysisContent = document.getElementById('analysis-content');
            analysisContainer.style.display = 'block';
            analysisContent.innerHTML = '<p>AI正在分析数据，请稍候...</p>';

            // 滚动到分析容器
            analysisContainer.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // 使用通用的AI分析函数
            analyzeWithAI('/current_control_circuit/analyze', {
                k1_current: k1_current,
                k01_current: k01_current
            }, selectedModelId,
                // 成功回调
                (renderedHTML) => {
                    displayAnalysisResult('analysis-container', 'analysis-content', renderedHTML);
                },
                // 错误回调
                (error) => {
                    displayAnalysisResult('analysis-container', 'analysis-content', 
                        `<p style="color: #e74c3c;">连接服务器时发生错误，请重试！</p>`);
                }
            );
        }

        // 完成实验
        function finishExperiment() {
            // 显示提交对话框
            document.getElementById('submission-modal').style.display = 'flex';
        }

        // 关闭提交对话框
        function closeSubmitModal() {
            document.getElementById('submission-modal').style.display = 'none';
            document.getElementById('submission-status').style.display = 'none';
            document.getElementById('submission-status').innerHTML = '';
        }

        // 提交实验数据
        function submitExperiment() {
            const studentId = document.getElementById('student-id').value.trim();
            const studentName = document.getElementById('student-name').value.trim();

            // 验证输入
            if (!studentId || !studentName) {
                document.getElementById('submission-status').style.display = 'block';
                document.getElementById('submission-status').style.backgroundColor = '#f8d7da';
                document.getElementById('submission-status').innerHTML = '错误：学号和姓名不能为空！';
                return;
            }

            // 获取实验数据
            const k1_inputs = document.getElementsByClassName('k1-current');
            const k01_inputs = document.getElementsByClassName('k01-current');

            const k1_current = Array.from(k1_inputs).map(input => parseFloat(input.value));
            const k01_current = Array.from(k01_inputs).map(input => parseFloat(input.value));

            // 获取已生成的图片数据
            const plotImage = document.getElementById('plot-image');
            let plotData = null;
            if (plotImage && plotImage.src && plotImage.style.display !== 'none') {
                if (plotImage.src.startsWith('data:image/png;base64,')) {
                    plotData = plotImage.src.split(',')[1];
                }
            }

            // 获取AI分析结果
            const analysisContent = document.getElementById('analysis-content');
            let analysisResult = null;
            if (analysisContent && analysisContent.innerHTML &&
                document.getElementById('analysis-container').style.display !== 'none') {
                analysisResult = analysisContent.innerText || analysisContent.textContent;
            }

            // 准备提交数据
            const submissionData = {
                student_id: studentId,
                student_name: studentName,
                k1_current: k1_current,
                k01_current: k01_current,
                plot_data: plotData,
                analysis_result: analysisResult
            };

            // 显示提交中状态
            document.getElementById('submission-status').style.display = 'block';
            document.getElementById('submission-status').style.backgroundColor = '#e8f4fc';
            document.getElementById('submission-status').innerHTML = '正在提交实验数据，请稍候...';

            // 使用通用提交处理函数
            handleExperimentSubmission('zhiliu_circuit', submissionData,
                // 成功回调
                (result) => {
                    document.getElementById('submission-status').style.backgroundColor = '#d4edda';
                    document.getElementById('submission-status').innerHTML = `恭喜！实验数据已成功提交。记录ID: ${result.record_id}`;

                    // 3秒后关闭对话框
                    setTimeout(() => {
                        closeSubmitModal();
                        alert('实验已成功完成并提交！');
                    }, 3000);
                },
                // 错误回调
                (error) => {
                    document.getElementById('submission-status').style.backgroundColor = '#f8d7da';
                    document.getElementById('submission-status').innerHTML = '错误：' + error;
                }
            );
        }
    </script>
</body>
</html>
