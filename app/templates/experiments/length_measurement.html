<!DOCTYPE html>
<html>

<head>
    <title>长度测量与不确定度计算实验 - 交互式实验平台</title>
    <meta name="author" content="朱瑞华">
    <meta name="last-modified" content="2024/10/30">
    <meta name="backup-date" content="2024/12/29">
    <meta charset="UTF-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/experiment.css') }}">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
    <!-- 添加中文字体支持 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;600&display=swap" rel="stylesheet">
    <!-- Vue.js CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>

</head>

<body>
    <div id="app" class="container">
        <h1>长度测量与不确定度计算实验</h1>
        <!-- 步骤1: 实验原理 -->
        <div class="step-container active" id="step1">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">不确定度理论基础</div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="step1-progress" style="width: 0%"></div>
            </div>

            <div class="substep-container active" id="step1-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">不确定度的基本概念</div>
                </div>
                <div class="substep-content">
                    <div class="instruction">
                        <h3>什么是不确定度？</h3>
                        <p>不确定度是表征测量结果可信程度的参数，反映了测量值的分散性。</p>

                        <div class="formula-box">
                            <h4>不确定度的分类：</h4>
                            <ul>
                                <li><strong>A类不确定度 (u<sub>A</sub>)</strong>：用统计方法计算的不确定度</li>
                                <li><strong>B类不确定度 (u<sub>B</sub>)</strong>：用非统计方法计算的不确定度</li>
                                <li><strong>C类不确定度 (u<sub>C</sub>)</strong>：合成不确定度</li>
                            </ul>
                        </div>
                    </div>
                    <navigation-component
                        :current-step="1"
                        :current-substep="1"
                        :total-substeps="3"
                        :show-prev="false"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="下一步"
                        @next="nextSubStep"
                        @prev="prevSubStep">
                    </navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step1-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">A类不确定度计算</div>
                </div>
                <div class="substep-content">
                    <div class="formula-box">
                        <h4>A类不确定度计算公式：</h4>
                        <p><strong>u<sub>A</sub> = s / √n</strong></p>
                        <p>其中：</p>
                        <ul>
                            <li>s = √[Σ(x<sub>i</sub> - x̄)² / (n-1)] （样本标准差）</li>
                            <li>x̄ = Σx<sub>i</sub> / n （算术平均值）</li>
                            <li>n = 测量次数</li>
                        </ul>
                    </div>
                    <div class="calculation-step">
                        <h4>计算步骤：</h4>
                        <ol>
                            <li>计算算术平均值 x̄</li>
                            <li>计算各测量值与平均值的偏差</li>
                            <li>计算偏差的平方和</li>
                            <li>计算样本标准差 s</li>
                            <li>计算A类不确定度 u<sub>A</sub></li>
                        </ol>
                    </div>
                    <navigation-component
                        :current-step="1"
                        :current-substep="2"
                        :total-substeps="3"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        next-text="下一步"
                        @next="nextSubStep"
                        @prev="prevSubStep">
                    </navigation-component>
                </div>
            </div>

            <div class="substep-container" id="step1-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">B类和C类不确定度</div>
                </div>
                <div class="substep-content">
                    <div class="formula-box">
                        <h4>B类不确定度：</h4>
                        <p><strong>u<sub>B</sub> = Δ / √3</strong></p>
                        <p>其中 Δ 为仪器的最大允许误差（示值误差）</p>

                        <h4>C类不确定度（合成不确定度）：</h4>
                        <p><strong>u<sub>C</sub> = √(u<sub>A</sub>² + u<sub>B</sub>²)</strong></p>
                    </div>
                    <div class="instruction">
                        <h4>常用仪器的示值误差：</h4>
                        <ul>
                            <li>50分度游标卡尺：Δ = 0.02 mm</li>
                            <li>螺旋测微计：Δ = 0.004 mm</li>
                            <li>数字式卡尺：Δ = 0.02 mm</li>
                        </ul>
                    </div>
                    <navigation-component
                        :current-step="1"
                        :current-substep="3"
                        :total-substeps="3"
                        :show-prev="true"
                        :show-next="true"
                        prev-text="上一步"
                        :next-text="'开始测量'"
                        :is-final-substep="true"
                        @next="nextStep"
                        @prev="prevSubStep">
                    </navigation-component>
                </div>
            </div>
        </div>

        <!-- 步骤2: 游标卡尺测量圆管体积 -->
        <div class="step-container" id="step2">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">游标卡尺测量圆管体积</div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="step2-progress" style="width: 0%"></div>
            </div>

            <!-- 操作指导 -->
            <div class="instruction">
                <h4>操作指导：</h4>
                <ul>
                    <li>用游标卡尺分别测量圆管的外径、内径和高，每项各测5次。</li>
                    <li>每次测量时注意卡尺零点校准，读数时视线要垂直于刻度线。</li>
                </ul>
            </div>
            <!-- 2.1 外径数据记录 -->
            <div class="substep-container active" id="step2-substep1">
                <h4>外径 D (mm) 数据记录</h4>
                <div class="measurement-table">
                    <table>
                        <thead>
                            <tr><th>测量次数</th><th>外径 D (mm)</th></tr>
                        </thead>
                        <tbody>
                            <tr><td>1</td><td><input type="number" step="0.01" class="vernier-D" required></td></tr>
                            <tr><td>2</td><td><input type="number" step="0.01" class="vernier-D" required></td></tr>
                            <tr><td>3</td><td><input type="number" step="0.01" class="vernier-D" required></td></tr>
                            <tr><td>4</td><td><input type="number" step="0.01" class="vernier-D" required></td></tr>
                            <tr><td>5</td><td><input type="number" step="0.01" class="vernier-D" required></td></tr>
                        </tbody>
                    </table>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="1"
                    :total-substeps="11"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="下一步"
                    :has-validation="true"
                    :validation-function="'checkVernierDData'"
                    :next-button-id="'next-btn-2-1'"
                    :is-step-start="true"
                    @next="nextSubStep"
                    @prev="prevStep"
                    @validate="validateData">
                </navigation-component>
            </div>
            <!-- 2.2 内径数据记录 -->
            <div class="substep-container" id="step2-substep2">
                <h4>内径 d (mm) 数据记录</h4>
                <div class="measurement-table">
                    <table>
                        <thead>
                            <tr><th>测量次数</th><th>内径 d (mm)</th></tr>
                        </thead>
                        <tbody>
                            <tr><td>1</td><td><input type="number" step="0.01" class="vernier-d" required></td></tr>
                            <tr><td>2</td><td><input type="number" step="0.01" class="vernier-d" required></td></tr>
                            <tr><td>3</td><td><input type="number" step="0.01" class="vernier-d" required></td></tr>
                            <tr><td>4</td><td><input type="number" step="0.01" class="vernier-d" required></td></tr>
                            <tr><td>5</td><td><input type="number" step="0.01" class="vernier-d" required></td></tr>
                        </tbody>
                    </table>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="2"
                    :total-substeps="11"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="下一步"
                    :has-validation="true"
                    :validation-function="'checkVernierdData'"
                    :next-button-id="'next-btn-2-2'"
                    @next="nextSubStep"
                    @prev="prevSubStep"
                    @validate="validateData">
                </navigation-component>
            </div>
            <!-- 2.3 高度数据记录 -->
            <div class="substep-container" id="step2-substep3">
                <h4>高 h (mm) 数据记录</h4>
                <div class="measurement-table">
                    <table>
                        <thead>
                            <tr><th>测量次数</th><th>高度 h (mm)</th></tr>
                        </thead>
                        <tbody>
                            <tr><td>1</td><td><input type="number" step="0.01" class="vernier-h" required></td></tr>
                            <tr><td>2</td><td><input type="number" step="0.01" class="vernier-h" required></td></tr>
                            <tr><td>3</td><td><input type="number" step="0.01" class="vernier-h" required></td></tr>
                            <tr><td>4</td><td><input type="number" step="0.01" class="vernier-h" required></td></tr>
                            <tr><td>5</td><td><input type="number" step="0.01" class="vernier-h" required></td></tr>
                        </tbody>
                    </table>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="3"
                    :total-substeps="11"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="下一步"
                    :has-validation="true"
                    :validation-function="'checkVernierhData'"
                    :next-button-id="'next-btn-2-3'"
                    @next="nextSubStep"
                    @prev="prevSubStep"
                    @validate="validateData">
                </navigation-component>
            </div>
            <!-- 2.4 外径平均值计算 -->
            <div class="substep-container" id="step2-substep4">
                <h4>外径平均值 D̄ 计算</h4>
                <div class="data-display">
                    <div id="D-data-display"></div>
                </div>
                <div class="uncertainty-container">
                    <div class="formula-box">
                        D̄ = (D₁ + D₂ + D₃ + D₄ + D₅) / 5
                    </div>
                    <div class="student-input">
                        <label>外径平均值 D̄ (mm)：</label>
                        <input type="number" step="0.001" id="student-mean-D" placeholder="输入计算结果"><br>

                    </div>
                    <div class="calculator-control">
                        <button class="calculator-toggle-btn" onclick="toggleCalculator('calc-2-4')">🧮 使用计算器辅助计算</button>
                    </div>
                    <div id="calc-2-4" class="calculator-container" style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;"></div>
                    <div id="mean-D-result" class="check-result" style="display: none;"></div>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="4"
                    :total-substeps="11"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="下一步"
                    :has-validation="true"
                    :validation-function="'checkMeanDCalculation'"
                    :next-button-id="'next-btn-2-4'"
                    @next="nextSubStep"
                    @prev="prevSubStep"
                    @validate="validateData">
                </navigation-component>
            </div>
            <!-- 2.5 内径平均值计算 -->
            <div class="substep-container" id="step2-substep5">
                <h4>内径平均值 d̄ 计算</h4>
                <div class="data-display">
                    <div id="d-data-display"></div>
                </div>
                <div class="uncertainty-container">
                    <div class="formula-box">
                        d̄ = (d₁ + d₂ + d₃ + d₄ + d₅) / 5
                    </div>
                    <div class="student-input">
                        <label>内径平均值 d̄ (mm)：</label>
                        <input type="number" step="0.001" id="student-mean-d" placeholder="输入计算结果"><br>

                    </div>
                    <div class="calculator-control">
                        <button class="calculator-toggle-btn" onclick="toggleCalculator('calc-2-5')">🧮 使用计算器辅助计算</button>
                    </div>
                    <div id="calc-2-5" class="calculator-container" style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;"></div>
                    <div id="mean-d-result" class="check-result" style="display: none;"></div>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="5"
                    :total-substeps="11"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="下一步"
                    :has-validation="true"
                    :validation-function="'checkMeandCalculation'"
                    :next-button-id="'next-btn-2-5'"
                    @next="nextSubStep"
                    @prev="prevSubStep"
                    @validate="validateData">
                </navigation-component>
            </div>
            <!-- 2.6 高度平均值计算 -->
            <div class="substep-container" id="step2-substep6">
                <h4>高度平均值 h̄ 计算</h4>
                <div class="data-display">
                    <div id="h-data-display"></div>
                </div>
                <div class="uncertainty-container">
                    <div class="formula-box">
                        h̄ = (h₁ + h₂ + h₃ + h₄ + h₅) / 5
                    </div>
                    <div class="student-input">
                        <label>高度平均值 h̄ (mm)：</label>
                        <input type="number" step="0.001" id="student-mean-h" placeholder="输入计算结果"><br>

                    </div>
                    <div class="calculator-control">
                        <button class="calculator-toggle-btn" onclick="toggleCalculator('calc-2-6')">🧮 使用计算器辅助计算</button>
                    </div>
                    <div id="calc-2-6" class="calculator-container" style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;"></div>
                    <div id="mean-h-result" class="check-result" style="display: none;"></div>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="6"
                    :total-substeps="11"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="下一步"
                    :has-validation="true"
                    :validation-function="'checkMeanhCalculation'"
                    :next-button-id="'next-btn-2-6'"
                    @next="nextSubStep"
                    @prev="prevSubStep"
                    @validate="validateData">
                </navigation-component>
            </div>

            <!-- 2.7 A类不确定度计算 -->
            <div class="substep-container" id="step2-substep7">
                <h4>A类不确定度计算</h4>
                <div class="uncertainty-container">
                    <h4>第二步：计算A类不确定度</h4>
                    <div class="formula-box">
                        uA_D = s_D / √5<br>
                        uA_d = s_d / √5<br>
                        uA_h = s_h / √5
                    </div>
                    <div class="student-input">
                        <label>外径A类不确定度 uA_D (mm)：</label>
                        <input type="number" step="0.0001" id="student-ua-D" placeholder="输入计算结果"><br>
                        <label>内径A类不确定度 uA_d (mm)：</label>
                        <input type="number" step="0.0001" id="student-ua-d" placeholder="输入计算结果"><br>
                        <label>高度A类不确定度 uA_h (mm)：</label>
                        <input type="number" step="0.0001" id="student-ua-h" placeholder="输入计算结果"><br>

                    </div>
                    <div class="calculator-control">
                        <button class="calculator-toggle-btn" onclick="toggleCalculator('calc-2-7')">🧮 使用计算器辅助计算</button>
                    </div>
                    <div id="calc-2-7" class="calculator-container" style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;"></div>
                    <div id="ua-ddh-result" class="check-result" style="display: none;"></div>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="7"
                    :total-substeps="11"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="下一步"
                    :has-validation="true"
                    :validation-function="'checkUaDdhCalculation'"
                    :next-button-id="'next-btn-2-7'"
                    @next="nextSubStep"
                    @prev="prevSubStep"
                    @validate="validateData">
                </navigation-component>
            </div>
            <!-- 2.8 B类不确定度计算 -->
            <div class="substep-container" id="step2-substep8">
                <h4>B类不确定度计算</h4>
                <div class="uncertainty-container">
                    <h4>第三步：计算B类不确定度</h4>
                    <div class="formula-box">
                        uB_D = Δ / √3 = 0.02 / √3<br>
                        uB_d = Δ / √3 = 0.02 / √3<br>
                        uB_h = Δ / √3 = 0.02 / √3
                    </div>
                    <div class="student-input">
                        <label>外径B类不确定度 uB_D (mm)：</label>
                        <input type="number" step="0.0001" id="student-ub-D" placeholder="输入计算结果"><br>
                        <label>内径B类不确定度 uB_d (mm)：</label>
                        <input type="number" step="0.0001" id="student-ub-d" placeholder="输入计算结果"><br>
                        <label>高度B类不确定度 uB_h (mm)：</label>
                        <input type="number" step="0.0001" id="student-ub-h" placeholder="输入计算结果"><br>

                    </div>
                    <div class="calculator-control">
                        <button class="calculator-toggle-btn" onclick="toggleCalculator('calc-2-8')">🧮 使用计算器辅助计算</button>
                    </div>
                    <div id="calc-2-8" class="calculator-container" style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;"></div>
                    <div id="ub-ddh-result" class="check-result" style="display: none;"></div>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="8"
                    :total-substeps="11"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="下一步"
                    :has-validation="true"
                    :validation-function="'checkUbDdhCalculation'"
                    :next-button-id="'next-btn-2-8'"
                    @next="nextSubStep"
                    @prev="prevSubStep"
                    @validate="validateData">
                </navigation-component>
            </div>
            <!-- 2.9 C类不确定度计算 -->
            <div class="substep-container" id="step2-substep9">
                <h4>C类不确定度计算</h4>
                <div class="uncertainty-container">
                    <h4>第四步：计算C类不确定度</h4>
                    <div class="formula-box">
                        uC_D = √(uA_D² + uB_D²)<br>
                        uC_d = √(uA_d² + uB_d²)<br>
                        uC_h = √(uA_h² + uB_h²)
                    </div>
                    <div class="student-input">
                        <label>外径C类不确定度 uC_D (mm)：</label>
                        <input type="number" step="0.0001" id="student-uc-D" placeholder="输入计算结果"><br>
                        <label>内径C类不确定度 uC_d (mm)：</label>
                        <input type="number" step="0.0001" id="student-uc-d" placeholder="输入计算结果"><br>
                        <label>高度C类不确定度 uC_h (mm)：</label>
                        <input type="number" step="0.0001" id="student-uc-h" placeholder="输入计算结果"><br>

                    </div>
                    <div class="calculator-control">
                        <button class="calculator-toggle-btn" onclick="toggleCalculator('calc-2-9')">🧮 使用计算器辅助计算</button>
                    </div>
                    <div id="calc-2-9" class="calculator-container" style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;"></div>
                    <div id="uc-ddh-result" class="check-result" style="display: none;"></div>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="9"
                    :total-substeps="11"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="下一步"
                    :has-validation="true"
                    :validation-function="'checkUcDdhCalculation'"
                    :next-button-id="'next-btn-2-9'"
                    @next="nextSubStep"
                    @prev="prevSubStep"
                    @validate="validateData">
                </navigation-component>
            </div>
            <!-- 2.10 体积计算 -->
            <div class="substep-container" id="step2-substep10">
                <h4>体积 V 计算</h4>
                <div class="uncertainty-container">
                    <h4>第五步：计算圆管体积</h4>
                    <div class="formula-box">
                        V = π/4 × (D̄² - d̄²) × h̄
                    </div>
                    <div class="student-input">
                        <label>体积 V (mm³)：</label>
                        <input type="number" step="0.01" id="student-V" placeholder="输入计算结果"><br>

                    </div>
                    <div class="calculator-control">
                        <button class="calculator-toggle-btn" onclick="toggleCalculator('calc-2-10')">🧮 使用计算器辅助计算</button>
                    </div>
                    <div id="calc-2-10" class="calculator-container" style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;"></div>
                    <div id="v-cylinder-result" class="check-result" style="display: none;"></div>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="10"
                    :total-substeps="11"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="下一步"
                    :has-validation="true"
                    :validation-function="'checkVCylinderCalculation'"
                    :next-button-id="'next-btn-2-10'"
                    @next="nextSubStep"
                    @prev="prevSubStep"
                    @validate="validateData">
                </navigation-component>
            </div>
            <!-- 2.11 体积不确定度计算 -->
            <div class="substep-container" id="step2-substep11">
                <h4>体积不确定度 uC_V 计算</h4>
                <div class="uncertainty-container">
                    <h4>第六步：计算体积的合成不确定度</h4>
                    <div class="formula-box">
                        u<sub>C,V</sub> = V × √[ (4(D̄<sup>2</sup>u<sub>C,D</sub><sup>2</sup> + d̄<sup>2</sup>u<sub>C,d</sub><sup>2</sup>)) / (D̄<sup>2</sup> - d̄<sup>2</sup>)<sup>2</sup> + (u<sub>C,h</sub>/h̄)<sup>2</sup> ]
                    </div>
                    <div class="student-input">
                        <label>体积合成不确定度 uC_V (mm³)：</label>
                        <input type="number" step="0.01" id="student-uc-V" placeholder="输入计算结果"><br>

                    </div>
                    <div class="calculator-control">
                        <button class="calculator-toggle-btn" onclick="toggleCalculator('calc-2-11')">🧮 使用计算器辅助计算</button>
                    </div>
                    <div id="calc-2-11" class="calculator-container" style="display: none; margin-top: 15px; padding: 15px; background: #f8f9fa; border-radius: 10px; border: 1px solid #e9ecef;"></div>
                    <div id="uc-v-cylinder-result" class="check-result" style="display: none;"></div>
                </div>
                <navigation-component
                    :current-step="2"
                    :current-substep="11"
                    :total-substeps="11"
                    :show-prev="true"
                    :show-next="true"
                    prev-text="上一步"
                    next-text="下一步"
                    :has-validation="true"
                    :validation-function="'checkUcVCylinderCalculation'"
                    :next-button-id="'next-btn-2-11'"
                    :is-final-substep="true"
                    @next="nextStep"
                    @prev="prevSubStep"
                    @validate="validateData">
                </navigation-component>
            </div>
        </div>

        <!-- 步骤3: 数据分析与结果 -->
        <div class="step-container" id="step3">
            <div class="step-header">
                <div class="step-number">3</div>
                <div class="step-title">数据分析与结果</div>
            </div>

            <h3>实验数据与计算结果汇总</h3>
            <div class="instruction" style="margin-bottom: 10px; color: #007bff; font-weight: bold;">建议手机横屏显示</div>
            <div class="table-container" id="summary-table-container">
                <table id="summary-table">
                    <thead>
                        <tr>
                            <th>项目</th>
                            <th>第1次</th>
                            <th>第2次</th>
                            <th>第3次</th>
                            <th>第4次</th>
                            <th>第5次</th>
                            <th>平均值</th>
                            <th>A类不确定度</th>
                            <th>B类不确定度</th>
                            <th>C类不确定度</th>
                        </tr>
                    </thead>
                    <tbody id="summary-table-body"></tbody>
                </table>
            </div>

            <div class="btn-container">
                <button onclick="generateSummaryTable()">生成数据汇总表</button>
                <button onclick="analyzeChangduData()" style="background-color: #27ae60; margin-left: 10px;">AI 分析数据</button>
            </div>

            <div id="changdu-analysis-container" style="display:none; margin-top:20px;">
                <h4>AI分析结果</h4>
                <div id="changdu-analysis-content"></div>
            </div>

            <navigation-component
                :current-step="3"
                :current-substep="1"
                :total-substeps="1"
                :show-prev="true"
                :show-next="true"
                prev-text="上一步"
                :next-text="'提交实验结果'"
                :is-step-start="true"
                @next="showSubmitModal"
                @prev="prevStep">
            </navigation-component>
        </div>

        <!-- 提交模态框 -->
        <experiment-submission-modal
            experiment-type="length_measurement"
            modal-id="length-measurement-submission-modal"
            :visible="showSubmissionModal"
            @submit="handleSubmitExperiment"
            @cancel="handleCancelSubmission"
            @success="handleSubmissionSuccess"
            ref="submissionModal">
        </experiment-submission-modal>

        <div class="footer">
            © 2024 | 物理实验基础课程平台 | 网页设计：朱瑞华
        </div>
    </div>

    <!-- 引入必要的库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjs/11.8.0/math.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/showdown/2.1.0/showdown.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/showdown-katex@0.8.0/dist/showdown-katex.min.js"></script>

    <script src="{{ url_for('static', filename='js/experiment.js') }}"></script>

    <script>
        // 等待DOM加载完成
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing Vue...');

            // Vue.js 应用程序
            const { createApp } = Vue;


            template: `
                <div class="substep-nav">
                    <button v-if="showPrev" @click="handlePrev" :class="prevButtonClass" style="color: white !important; font-family: 'Noto Sans SC', 'Microsoft YaHei', 'SimHei', sans-serif !important; font-weight: 400 !important;">
                        {{ "{{" }} prevText || '上一步' {{ "}}" }}
                    </button>
                    <div v-else></div>

                    <div v-if="hasValidation" style="display: flex; gap: 10px;">
                        <button @click="handleValidation" class="validation-btn" style="color: white !important; font-family: 'Noto Sans SC', 'Microsoft YaHei', 'SimHei', sans-serif !important; font-weight: 400 !important;">
                            验证数据
                        </button>
                        <button
                            v-if="showNext"
                            @click="handleNext"
                            :disabled="isNextDisabled"
                            :id="nextButtonId"
                            :class="nextButtonClass"
                            style="color: white !important; font-family: 'Noto Sans SC', 'Microsoft YaHei', 'SimHei', sans-serif !important; font-weight: 400 !important;">
                            {{ "{{" }} nextText || '下一步' {{ "}}" }}
                        </button>
                    </div>
                    <button
                        v-else-if="showNext"
                        @click="handleNext"
                        :class="nextButtonClass"
                        style="color: white !important; font-family: 'Noto Sans SC', 'Microsoft YaHei', 'SimHei', sans-serif !important; font-weight: 400 !important;">
                        {{ "{{" }} nextText || '下一步' {{ "}}" }}
                    </button>
                    <div v-else></div>
                </div>
            `,
            mounted() {
                console.log('NavigationComponent mounted');
                console.log('Props:', this.$props);
                console.log('prevText:', this.prevText);
                console.log('nextText:', this.nextText);

                // 确保按钮文字正确显示
                this.$nextTick(() => {
                    const buttons = this.$el.querySelectorAll('button');
                    buttons.forEach((button, index) => {
                        console.log(`Button ${index} text content:`, button.textContent);
                        console.log(`Button ${index} innerHTML:`, button.innerHTML);

                        // 强制设置文字内容
                        if (button.textContent.trim() === '') {
                            if (button.classList.contains('validation-btn')) {
                                button.textContent = '验证数据';
                            } else if (index === 0 && this.showPrev) {
                                button.textContent = this.prevText || '上一步';
                            } else if (this.showNext) {
                                button.textContent = this.nextText || '下一步';
                            }
                        }
                    });
                });
            },
            computed: {
                prevButtonClass() {
                    return this.isStepStart ? 'prev-btn' : '';
                },
                nextButtonClass() {
                    return '';
                },
                isNextDisabled() {
                    return this.hasValidation && this.nextButtonId;
                }
            },
            methods: {
                handlePrev() {
                    if (this.isStepStart) {
                        this.$emit('prev', this.currentStep);
                    } else {
                        this.$emit('prev', `step${this.currentStep}`, this.currentSubstep, this.totalSubsteps);
                    }
                },
                handleNext() {
                    if (this.isFinalSubstep) {
                        this.$emit('next', this.currentStep);
                    } else {
                        this.$emit('next', `step${this.currentStep}`, this.currentSubstep, this.totalSubsteps);
                    }
                },
                handleValidation() {
                    console.log('handleValidation called with function:', this.validationFunction);

                    // 只发出事件给父组件，避免重复调用
                    this.$emit('validate', this.validationFunction);
                }
            }
        };

        // Vue 应用程序
        const app = createApp({
            components: {
                'navigation-component': ExperimentNavigationComponent,
                'experiment-submission-modal': ExperimentSubmissionModalComponent
            },
            data() {
                return {
                    showSubmissionModal: false
                };
            },
            methods: {
                updateProgress(step, currentSubstep, totalSubsteps) {
                    const progress = document.getElementById(`${step}-progress`);
                    if (progress) {
                        const percentage = (currentSubstep / totalSubsteps) * 100;
                        progress.style.width = `${percentage}%`;
                        console.log(`Progress updated: ${step} - ${currentSubstep}/${totalSubsteps} = ${percentage}%`);
                    }
                },
                nextSubStep(step, substep, total) {
                    document.getElementById(`${step}-substep${substep}`).classList.remove('active');
                    document.getElementById(`${step}-substep${substep + 1}`).classList.add('active');
                    this.updateProgress(step, substep + 1, total);
                },
                prevSubStep(step, substep, total) {
                    document.getElementById(`${step}-substep${substep}`).classList.remove('active');
                    document.getElementById(`${step}-substep${substep - 1}`).classList.add('active');
                    this.updateProgress(step, substep - 1, total);
                },
                nextStep(currentStep) {
                    document.getElementById(`step${currentStep}`).classList.remove('active');
                    document.getElementById(`step${currentStep + 1}`).classList.add('active');

                    // 更新进度条
                    if (currentStep === 1) {
                        this.updateProgress('step1', 3, 3); // 第一步完成
                        this.updateProgress('step2', 1, 11); // 第二步开始
                    } else if (currentStep === 2) {
                        this.updateProgress('step2', 11, 11); // 第二步完成
                    }
                },
                prevStep(currentStep) {
                    document.getElementById(`step${currentStep}`).classList.remove('active');
                    document.getElementById(`step${currentStep - 1}`).classList.add('active');

                    // 更新进度条
                    if (currentStep === 2) {
                        this.updateProgress('step1', 3, 3); // 回到第一步末尾
                        this.updateProgress('step2', 0, 11); // 第二步重置
                    } else if (currentStep === 3) {
                        this.updateProgress('step2', 11, 11); // 回到第二步末尾
                    }
                },
                validateData(functionName) {
                    // 验证数据的通用方法
                    console.log('Validating with function:', functionName);
                    if (functionName && window[functionName]) {
                        console.log('Calling validation function:', functionName);
                        window[functionName]();
                    } else {
                        console.error('Validation function not found:', functionName);
                    }
                },
                showSubmitModal() {
                    this.showSubmissionModal = true;
                },
                handleSubmitExperiment(submissionData) {
                    // 收集实验数据
                    const experimentData = this.collectExperimentData();

                    // 调用提交函数
                    this.submitExperiment(submissionData.studentId, submissionData.studentName, experimentData);
                },
                handleCancelSubmission() {
                    this.showSubmissionModal = false;
                },
                handleSubmissionSuccess() {
                    this.showSubmissionModal = false;
                    alert('实验已成功完成并提交！');
                },
                collectExperimentData() {
                    // 收集所有实验数据
                    const tbody = document.getElementById('summary-table-body');
                    let summaryData = [];
                    if (tbody) {
                        for (let row of tbody.rows) {
                            summaryData.push({
                                item: row.cells[0].innerText,
                                value: row.cells[1].innerText
                            });
                        }
                    }

                    return {
                        summaryData: summaryData,
                        vernierData: window.vernierData || [],
                        micrometerData: window.micrometerData || [],
                        digitalData: window.digitalData || [],
                        uncertaintyResults: window.correctResults || {}
                    };
                },
                async submitExperiment(studentId, studentName, experimentData) {
                    try {
                        // 显示提交状态
                        this.$refs.submissionModal.showStatus('正在提交实验数据，请稍候...', '#e8f4fc', '#333');

                        // 模拟提交过程
                        await new Promise(resolve => setTimeout(resolve, 1500));

                        // 显示成功状态
                        this.$refs.submissionModal.showSuccess('恭喜！实验数据已成功提交。');

                    } catch (error) {
                        // 显示错误状态
                        this.$refs.submissionModal.showError('提交失败：' + error.message);
                    }
                },
                setupProgressTracking() {
                    // 为所有导航组件添加进度跟踪
                    const navigationComponents = document.querySelectorAll('navigation-component');
                    console.log('Found navigation components:', navigationComponents.length);
                }
            },
            mounted() {
                // 初始化进度条
                this.updateProgress('step1', 1, 3);  // 第一步初始进度
                this.updateProgress('step2', 0, 11); // 第二步初始进度
                console.log('Vue app mounted, progress bars initialized');

                // 监听导航事件来更新进度条
                this.setupProgressTracking();
            }
        });

        app.mount('#app');

        // 调试信息
        console.log('Vue app mounted successfully');
        console.log('NavigationComponent:', NavigationComponent);

        // 确保验证函数在全局作用域中可用
        console.log('Checking validation functions:');
        console.log('checkVernierDData:', typeof window.checkVernierDData);
        console.log('checkVernierdData:', typeof window.checkVernierdData);
        console.log('checkVernierhData:', typeof window.checkVernierhData);

        // 全局变量
        let vernierData = [];
        let correctResults = {};

        // 步骤导航函数已移至Vue应用程序中

        // 数据验证函数 - 使用通用验证函数
        window.checkVernierDData = function() {
            const data = validateMeasurementData('vernier-D', '外径', 'next-btn-2-1', 'D-data-display');
            if (data) {
                window.vernierDData = data;
            }
        };

        window.checkVernierdData = function() {
            validateMeasurementData('vernier-d', '内径', 'next-btn-2-2', 'd-data-display');
        };

        window.checkVernierhData = function() {
            validateMeasurementData('vernier-h', '高度', 'next-btn-2-3', 'h-data-display');
        };



        // 计算检查函数
        window.checkMeanDCalculation = function() {
            // 直接从输入框读取数据
            const inputs = document.getElementsByClassName('vernier-D');
            let D = [];
            for (let input of inputs) {
                if (!input.value || isNaN(parseFloat(input.value))) {
                    alert('请填写所有外径数据，且数据必须为有效数字！');
                    return;
                }
                D.push(parseFloat(input.value));
            }
            const studentMeanD = parseFloat(document.getElementById('student-mean-D').value);
            if (isNaN(studentMeanD)) {
                alert('请输入外径平均值！');
                return;
            }
            const meanD = D.reduce((a, b) => a + b, 0) / 5;
            const tol = 0.001;
            const isD = Math.abs(studentMeanD - meanD) < tol;
            const resultDiv = document.getElementById('mean-D-result');
            resultDiv.style.display = 'block';
            if (isD) {
                resultDiv.innerHTML = `✅ 计算正确！外径 D̄ = ${meanD.toFixed(3)} mm`;
                document.getElementById('next-btn-2-4').disabled = false;
                window.meanD = meanD;
            } else {
                resultDiv.innerHTML = `❌ 计算有误。外径 D̄ 正确值为 ${meanD.toFixed(3)} mm`;
                document.getElementById('next-btn-2-4').disabled = true;
            }
        };

        window.checkMeandCalculation = function() {
            const inputs = document.getElementsByClassName('vernier-d');
            let d = [];
            for (let input of inputs) {
                if (!input.value || isNaN(parseFloat(input.value))) {
                    alert('请填写所有内径数据，且数据必须为有效数字！');
                    return;
                }
                d.push(parseFloat(input.value));
            }
            const studentMeand = parseFloat(document.getElementById('student-mean-d').value);
            if (isNaN(studentMeand)) {
                alert('请输入内径平均值！');
                return;
            }
            const meand = d.reduce((a, b) => a + b, 0) / 5;
            const tol = 0.001;
            const isd = Math.abs(studentMeand - meand) < tol;
            const resultDiv = document.getElementById('mean-d-result');
            resultDiv.style.display = 'block';
            if (isd) {
                resultDiv.innerHTML = `✅ 计算正确！内径 d̄ = ${meand.toFixed(3)} mm`;
                document.getElementById('next-btn-2-5').disabled = false;
                window.meand = meand;
            } else {
                resultDiv.innerHTML = `❌ 计算有误。内径 d̄ 正确值为 ${meand.toFixed(3)} mm`;
                document.getElementById('next-btn-2-5').disabled = true;
            }
        };

        window.checkMeanhCalculation = function() {
            const inputs = document.getElementsByClassName('vernier-h');
            let h = [];
            for (let input of inputs) {
                if (!input.value || isNaN(parseFloat(input.value))) {
                    alert('请填写所有高度数据，且数据必须为有效数字！');
                    return;
                }
                h.push(parseFloat(input.value));
            }
            const studentMeanh = parseFloat(document.getElementById('student-mean-h').value);
            if (isNaN(studentMeanh)) {
                alert('请输入高度平均值！');
                return;
            }
            const meanh = h.reduce((a, b) => a + b, 0) / 5;
            const tol = 0.001;
            const ish = Math.abs(studentMeanh - meanh) < tol;
            const resultDiv = document.getElementById('mean-h-result');
            resultDiv.style.display = 'block';
            if (ish) {
                resultDiv.innerHTML = `✅ 计算正确！高度 h̄ = ${meanh.toFixed(3)} mm`;
                document.getElementById('next-btn-2-6').disabled = false;
                window.meanh = meanh;
            } else {
                resultDiv.innerHTML = `❌ 计算有误。高度 h̄ 正确值为 ${meanh.toFixed(3)} mm`;
                document.getElementById('next-btn-2-6').disabled = true;
            }
        };



        window.checkUaDdhCalculation = function() {
            // Read student inputs
            const studentUaD = parseFloat(document.getElementById('student-ua-D').value);
            const studentUad = parseFloat(document.getElementById('student-ua-d').value);
            const studentUah = parseFloat(document.getElementById('student-ua-h').value);

            if (isNaN(studentUaD) || isNaN(studentUad) || isNaN(studentUah)) {
                alert('请输入所有A类不确定度的有效数值！');
                return;
            }

            // Get data and means
            const D_inputs = document.getElementsByClassName('vernier-D');
            const d_inputs = document.getElementsByClassName('vernier-d');
            const h_inputs = document.getElementsByClassName('vernier-h');

            let D_data = [], d_data = [], h_data = [];
            for(let i of D_inputs) D_data.push(parseFloat(i.value));
            for(let i of d_inputs) d_data.push(parseFloat(i.value));
            for(let i of h_inputs) h_data.push(parseFloat(i.value));

            // check if any data is NaN
            if (D_data.some(isNaN) || d_data.some(isNaN) || h_data.some(isNaN)) {
                alert('请确保所有测量数据都已正确填写！');
                return;
            }

            const meanD = window.meanD;
            const meand = window.meand;
            const meanh = window.meanh;

            if (meanD === undefined || meand === undefined || meanh === undefined) {
                alert('请先完成所有平均值的计算！');
                return;
            }

            // Calculate correct standard deviations and Ua
            const stdD = calculateStdDev(D_data, meanD);
            const stdd = calculateStdDev(d_data, meand);
            const stdh = calculateStdDev(h_data, meanh);

            const correctUaD = stdD / Math.sqrt(D_data.length);
            const correctUad = stdd / Math.sqrt(d_data.length);
            const correctUah = stdh / Math.sqrt(h_data.length);

            // Store for next steps
            window.uA_D = correctUaD;
            window.uA_d = correctUad;
            window.uA_h = correctUah;

            // Compare and give feedback
            const tolerance = 0.0001;
            const isUaDCorrect = Math.abs(studentUaD - correctUaD) < tolerance;
            const isUadCorrect = Math.abs(studentUad - correctUad) < tolerance;
            const isUahCorrect = Math.abs(studentUah - correctUah) < tolerance;

            const resultDiv = document.getElementById('ua-ddh-result');
            resultDiv.style.display = 'block';

            let resultHTML = '<h4>计算结果检查：</h4>';

            resultHTML += isUaDCorrect ?
                `✅ 外径A类不确定度正确 (${correctUaD.toFixed(5)} mm)<br>` :
                `❌ 外径A类不确定度有误，正确答案：${correctUaD.toFixed(5)} mm<br>`;

            resultHTML += isUadCorrect ?
                `✅ 内径A类不确定度正确 (${correctUad.toFixed(5)} mm)<br>` :
                `❌ 内径A类不确定度有误，正确答案：${correctUad.toFixed(5)} mm<br>`;

            resultHTML += isUahCorrect ?
                `✅ 高度A类不确定度正确 (${correctUah.toFixed(5)} mm)<br>` :
                `❌ 高度A类不确定度有误，正确答案：${correctUah.toFixed(5)} mm<br>`;

            const allUaCorrect = isUaDCorrect && isUadCorrect && isUahCorrect;

            if (allUaCorrect) {
                resultDiv.style.backgroundColor = '#d4edda';
                resultDiv.style.color = '#155724';
                resultHTML += '<br><strong>🎉 恭喜！所有计算都正确！</strong>';
                document.getElementById('next-btn-2-7').disabled = false;
            } else {
                resultDiv.style.backgroundColor = '#f8d7da';
                resultDiv.style.color = '#721c24';
                resultHTML += '<br><strong>请检查计算过程，修正错误后重新提交。</strong>';
                document.getElementById('next-btn-2-7').disabled = true;
            }

            resultDiv.innerHTML = resultHTML;
        };

        window.checkUbDdhCalculation = function() {
            const studentUbD = parseFloat(document.getElementById('student-ub-D').value);
            const studentUbd = parseFloat(document.getElementById('student-ub-d').value);
            const studentUbh = parseFloat(document.getElementById('student-ub-h').value);

            if (isNaN(studentUbD) || isNaN(studentUbd) || isNaN(studentUbh)) {
                alert('请输入有效的数值！');
                return;
            }

            const correctUb = 0.02 / Math.sqrt(3);

            // Store for next steps
            window.uB_D = correctUb;
            window.uB_d = correctUb;
            window.uB_h = correctUb;

            const tolerance = 0.0001;
            const isUbDCorrect = Math.abs(studentUbD - correctUb) < tolerance;
            const isUbdCorrect = Math.abs(studentUbd - correctUb) < tolerance;
            const isUbhCorrect = Math.abs(studentUbh - correctUb) < tolerance;

            const resultDiv = document.getElementById('ub-ddh-result');
            resultDiv.style.display = 'block';

            let resultHTML = '<h4>计算结果检查：</h4>';

            resultHTML += isUbDCorrect ?
                `✅ 外径B类不确定度正确 (${correctUb.toFixed(5)} mm)<br>` :
                `❌ 外径B类不确定度有误，正确答案：${correctUb.toFixed(5)} mm<br>`;

            resultHTML += isUbdCorrect ?
                `✅ 内径B类不确定度正确 (${correctUb.toFixed(5)} mm)<br>` :
                `❌ 内径B类不确定度有误，正确答案：${correctUb.toFixed(5)} mm<br>`;

            resultHTML += isUbhCorrect ?
                `✅ 高度B类不确定度正确 (${correctUb.toFixed(5)} mm)<br>` :
                `❌ 高度B类不确定度有误，正确答案：${correctUb.toFixed(5)} mm<br>`;

            const allUbCorrect = isUbDCorrect && isUbdCorrect && isUbhCorrect;

            if (allUbCorrect) {
                resultDiv.style.backgroundColor = '#d4edda';
                resultDiv.style.color = '#155724';
                resultHTML += '<br><strong>🎉 恭喜！所有计算都正确！</strong>';
                document.getElementById('next-btn-2-8').disabled = false;
            } else {
                resultDiv.style.backgroundColor = '#f8d7da';
                resultDiv.style.color = '#721c24';
                resultHTML += '<br><strong>请检查计算过程，修正错误后重新提交。</strong>';
                document.getElementById('next-btn-2-8').disabled = true;
            }

            resultDiv.innerHTML = resultHTML;
        }

        window.checkUcDdhCalculation = function() {
            const studentUcD = parseFloat(document.getElementById('student-uc-D').value);
            const studentUcd = parseFloat(document.getElementById('student-uc-d').value);
            const studentUch = parseFloat(document.getElementById('student-uc-h').value);

            if (isNaN(studentUcD) || isNaN(studentUcd) || isNaN(studentUch)) {
                alert('请输入所有C类不确定度的有效数值！');
                return;
            }

            // Check if previous steps are done
            if (window.uA_D === undefined || window.uB_D === undefined) {
                alert('请先完成A类和B类不确定度的计算！');
                return;
            }

            const correctUcD = Math.sqrt(Math.pow(window.uA_D, 2) + Math.pow(window.uB_D, 2));
            const correctUcd = Math.sqrt(Math.pow(window.uA_d, 2) + Math.pow(window.uB_d, 2));
            const correctUch = Math.sqrt(Math.pow(window.uA_h, 2) + Math.pow(window.uB_h, 2));

            // Store for next steps
            window.uC_D = correctUcD;
            window.uC_d = correctUcd;
            window.uC_h = correctUch;

            const tolerance = 0.0001;
            const isUcDCorrect = Math.abs(studentUcD - correctUcD) < tolerance;
            const isUcdCorrect = Math.abs(studentUcd - correctUcd) < tolerance;
            const isUchCorrect = Math.abs(studentUch - correctUch) < tolerance;

            const resultDiv = document.getElementById('uc-ddh-result');
            resultDiv.style.display = 'block';

            let resultHTML = '<h4>计算结果检查：</h4>';

            resultHTML += isUcDCorrect ?
                `✅ 外径C类不确定度正确 (${correctUcD.toFixed(5)} mm)<br>` :
                `❌ 外径C类不确定度有误，正确答案：${correctUcD.toFixed(5)} mm<br>`;

            resultHTML += isUcdCorrect ?
                `✅ 内径C类不确定度正确 (${correctUcd.toFixed(5)} mm)<br>` :
                `❌ 内径C类不确定度有误，正确答案：${correctUcd.toFixed(5)} mm<br>`;

            resultHTML += isUchCorrect ?
                `✅ 高度C类不确定度正确 (${correctUch.toFixed(5)} mm)<br>` :
                `❌ 高度C类不确定度有误，正确答案：${correctUch.toFixed(5)} mm<br>`;

            const allUcCorrect = isUcDCorrect && isUcdCorrect && isUchCorrect;

            if (allUcCorrect) {
                resultDiv.style.backgroundColor = '#d4edda';
                resultDiv.style.color = '#155724';
                resultHTML += '<br><strong>🎉 恭喜！所有计算都正确！</strong>';
                document.getElementById('next-btn-2-9').disabled = false;
                // 自动进入体积V计算
                setTimeout(function() {
                    nextSubStep('step2', 9, 11);
                }, 1200);
            } else {
                resultDiv.style.backgroundColor = '#f8d7da';
                resultDiv.style.color = '#721c24';
                resultHTML += '<br><strong>请检查计算过程，修正错误后重新提交。</strong>';
                document.getElementById('next-btn-2-9').disabled = true;
            }

            resultDiv.innerHTML = resultHTML;
        };



        // 其他必要的函数（导航函数已移至Vue应用程序中）

        // 体积计算检查函数
        window.checkVCylinderCalculation = function() {
            // 需要 meanD, meand, meanh
            const meanD = window.meanD;
            const meand = window.meand;
            const meanh = window.meanh;
            if (meanD === undefined || meand === undefined || meanh === undefined) {
                alert('请先完成平均值的计算！');
                return;
            }
            const studentV = parseFloat(document.getElementById('student-V').value);
            if (isNaN(studentV)) {
                alert('请输入体积！');
                return;
            }
            const V = Math.PI / 4 * (Math.pow(meanD, 2) - Math.pow(meand, 2)) * meanh;
            window.cylinderV = V;
            const tol = 0.01;
            const isV = Math.abs(studentV - V) < tol;
            const resultDiv = document.getElementById('v-cylinder-result');
            resultDiv.style.display = 'block';
            if (isV) {
                resultDiv.innerHTML = `✅ 计算正确！体积 V = ${V.toFixed(2)} mm³`;
                document.getElementById('next-btn-2-10').disabled = false;
                // 自动进入体积不确定度计算
                setTimeout(function() {
                    nextSubStep('step2', 10, 11);
                }, 1200);
            } else {
                resultDiv.innerHTML = `❌ 计算有误。体积 V 正确值为 ${V.toFixed(2)} mm³`;
                document.getElementById('next-btn-2-10').disabled = true;
            }
        };

        // 体积不确定度检查函数
        window.checkUcVCylinderCalculation = function() {
            const V = window.cylinderV;
            const meanD = window.meanD;
            const meand = window.meand;
            const meanh = window.meanh;
            const uC_D = window.uC_D;
            const uC_d = window.uC_d;
            const uC_h = window.uC_h;
            if ([V, meanD, meand, meanh, uC_D, uC_d, uC_h].some(v => v === undefined)) {
                alert('请先完成前面的计算！');
                return;
            }
            const studentUcV = parseFloat(document.getElementById('student-uc-V').value);
            if (isNaN(studentUcV)) {
                alert('请输入体积不确定度！');
                return;
            }
            const d_squared_diff = Math.pow(meanD, 2) - Math.pow(meand, 2);
            const d_unc_term = 4 * (Math.pow(meanD * uC_D, 2) + Math.pow(meand * uC_d, 2));
            const relUnc_squared = (d_unc_term / Math.pow(d_squared_diff, 2)) + Math.pow(uC_h / meanh, 2);
            const correctUcV = V * Math.sqrt(relUnc_squared);
            const tol = 0.01;
            const isUcV = Math.abs(studentUcV - correctUcV) < tol;
            const resultDiv = document.getElementById('uc-v-cylinder-result');
            resultDiv.style.display = 'block';
            if (isUcV) {
                resultDiv.innerHTML = `✅ 计算正确！体积合成不确定度 uC_V = ${correctUcV.toFixed(2)} mm³`;
                document.getElementById('next-btn-2-11').disabled = false;
            } else {
                resultDiv.innerHTML = `❌ 计算有误。体积合成不确定度 uC_V 正确值为 ${correctUcV.toFixed(2)} mm³`;
                document.getElementById('next-btn-2-11').disabled = true;
            }
        };

        // 生成数据汇总表
        window.generateSummaryTable = function() {
            const D = Array.from(document.getElementsByClassName('vernier-D')).map(i => i.value);
            const d = Array.from(document.getElementsByClassName('vernier-d')).map(i => i.value);
            const h = Array.from(document.getElementsByClassName('vernier-h')).map(i => i.value);
            const V = window.cylinderV;
            const uC_V = (window.cylinderV && window.meanD && window.meand && window.meanh && window.uC_D && window.uC_d && window.uC_h)
                ? (V * Math.sqrt(Math.pow(2 * window.uC_D / window.meanD, 2) + Math.pow(2 * window.uC_d / window.meand, 2) + Math.pow(window.uC_h / window.meanh, 2))) : undefined;

            const dataConfig = [
                {
                    label: '外径 D',
                    measurements: D,
                    mean: window.meanD,
                    uA: window.uA_D,
                    uB: window.uB_D,
                    uC: window.uC_D,
                    unit: 'mm'
                },
                {
                    label: '内径 d',
                    measurements: d,
                    mean: window.meand,
                    uA: window.uA_d,
                    uB: window.uB_d,
                    uC: window.uC_d,
                    unit: 'mm'
                },
                {
                    label: '高度 h',
                    measurements: h,
                    mean: window.meanh,
                    uA: window.uA_h,
                    uB: window.uB_h,
                    uC: window.uC_h,
                    unit: 'mm'
                },
                {
                    label: '体积 V',
                    measurements: [null, null, null, null, null],
                    mean: V,
                    uA: undefined,
                    uB: undefined,
                    uC: uC_V,
                    unit: 'mm³'
                }
            ];

            generateExperimentSummaryTable('summary-table-body', dataConfig);
        };

        // AI分析函数（模拟，实际应POST到后端）
        window.analyzeChangduData = function() {
            const tbody = document.getElementById('summary-table-body');
            let summaryText = '';
            for (let row of tbody.rows) {
                summaryText += row.cells[0].innerText + ': ' + row.cells[1].innerText + '\n';
            }
            // 这里应POST到后端，返回AI分析内容
            // 示例：
            document.getElementById('changdu-analysis-container').style.display = 'block';
            document.getElementById('changdu-analysis-content').innerHTML = '<p>AI分析功能待接入后端。当前数据如下：<br><pre>' + summaryText + '</pre></p>';
        };

        // 提交实验结果弹窗（showSubmitModal已移至Vue应用程序中）
        function closeChangduSubmitModal() {
            closeExperimentSubmitModal('changdu-submission-modal', 'changdu-submission-status');
        }

        // 提交实验结果（使用通用函数）
        function submitChangduExperiment() {
            function collectChangduData() {
                const tbody = document.getElementById('summary-table-body');
                let summaryData = [];
                for (let row of tbody.rows) {
                    summaryData.push({item: row.cells[0].innerText, value: row.cells[1].innerText});
                }
                return { summaryData };
            }

            submitExperimentData(
                'changdu_measurement',
                'changdu-student-id',
                'changdu-student-name',
                'changdu-submission-status',
                collectChangduData
            );
        }

        }); // 结束 DOMContentLoaded 事件监听器
    </script>
</body>
</html>
