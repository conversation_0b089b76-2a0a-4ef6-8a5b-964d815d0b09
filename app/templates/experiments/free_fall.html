<!-- ./app/templates/experiments/free_fall.html -->
{% from 'macros/experiment_macros.html' import render_model_selector, render_modal, render_analysis_container %}

<!DOCTYPE html>
<html>
<head>
    <title>自由落体实验 - 交互式实验平台</title>
    <meta name="author" content="自动生成">
    <meta name="last-modified" content="2024/10/30">
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/experiment.css') }}">
    <!-- 引入必要的库 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.css">
</head>

<body>
    <div class="container">
        <h1>自由落体实验</h1>

        <!-- 步骤1: 实验原理 -->
        <div class="step-container active" id="step1">
            <div class="step-header">
                <div class="step-number">1</div>
                <div class="step-title">实验原理与目的</div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="step1-progress" style="width: 0%"></div>
            </div>

            <div class="substep-container active" id="step1-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">自由落体运动规律</div>
                </div>
                <div class="substep-content">
                    <div class="formula-display">
                        <h3>自由落体运动基本公式</h3>
                        <div class="katex-display">
                            <span class="katex">h = \frac{1}{2}gt^2</span>
                        </div>
                        <p>其中：h 为下落高度，g 为重力加速度，t 为下落时间</p>
                    </div>

                    <div class="instruction">
                        <h3>实验目的：</h3>
                        <ol>
                            <li>验证自由落体运动规律</li>
                            <li>测量当地重力加速度 g</li>
                            <li>学习光电计时器的使用方法</li>
                            <li>掌握数据处理和误差分析</li>
                        </ol>
                    </div>

                    <div class="substep-nav">
                        <div></div>
                        <button onclick="nextSubStep('step1', 1, 4)">下一步</button>
                    </div>
                </div>
            </div>

            <div class="substep-container" id="step1-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">光电计时器原理</div>
                </div>
                <div class="substep-content">
                    <div class="measurement-setup">
                        <h3>光电计时器工作原理：</h3>
                        <p>光电计时器利用光电门检测物体通过的时间间隔，具有高精度、自动化的特点。</p>
                        
                        <h4>主要组成部分：</h4>
                        <ul>
                            <li><strong>光电门</strong>：由发光二极管和光敏二极管组成</li>
                            <li><strong>计时器</strong>：精确测量时间间隔</li>
                            <li><strong>控制面板</strong>：设置测量模式和参数</li>
                            <li><strong>数据接口</strong>：输出测量结果</li>
                        </ul>

                        <h4>测量原理：</h4>
                        <p>当钢球通过光电门时，会遮挡光线，产生电信号变化，计时器记录这个时间间隔。</p>
                    </div>

                    <div class="substep-nav">
                        <button onclick="prevSubStep('step1', 2, 4)">上一步</button>
                        <button onclick="nextSubStep('step1', 2, 4)">下一步</button>
                    </div>
                </div>
            </div>

            <div class="substep-container" id="step1-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">实验装置</div>
                </div>
                <div class="substep-content">
                    <div class="instruction">
                        <h3>实验装置组成：</h3>
                        <ul>
                            <li><strong>自由落体实验仪</strong>：提供垂直下落轨道</li>
                            <li><strong>光电计时器</strong>：精确测量下落时间</li>
                            <li><strong>钢球</strong>：下落物体（直径约1cm）</li>
                            <li><strong>米尺</strong>：测量下落高度</li>
                            <li><strong>水平仪</strong>：调节实验仪水平</li>
                        </ul>
                    </div>

                    <div class="warning">
                        <strong>注意事项：</strong>
                        <ul>
                            <li>确保实验环境无风，避免空气阻力影响</li>
                            <li>光电门要保持清洁，避免灰尘影响测量</li>
                            <li>钢球表面要光滑，无划痕</li>
                        </ul>
                    </div>

                    <div class="substep-nav">
                        <button onclick="prevSubStep('step1', 3, 4)">上一步</button>
                        <button onclick="nextSubStep('step1', 3, 4)">下一步</button>
                    </div>
                </div>
            </div>

            <div class="substep-container" id="step1-substep4">
                <div class="substep-header">
                    <div class="substep-number">4</div>
                    <div class="substep-title">数据处理方法</div>
                </div>
                <div class="substep-content">
                    <div class="instruction">
                        <h3>数据处理步骤：</h3>
                        <ol>
                            <li>记录不同高度下的下落时间</li>
                            <li>计算每次测量的重力加速度</li>
                            <li>计算平均值和标准偏差</li>
                            <li>分析误差来源</li>
                        </ol>
                    </div>

                    <div class="formula-display">
                        <h3>重力加速度计算公式</h3>
                        <div class="katex-display">
                            <span class="katex">g = \frac{2h}{t^2}</span>
                        </div>
                        <p>平均重力加速度：<span class="katex">\bar{g} = \frac{1}{n}\sum_{i=1}^{n} g_i</span></p>
                    </div>

                    <div class="substep-nav">
                        <button onclick="prevSubStep('step1', 4, 4)">上一步</button>
                        <button onclick="nextStep(1)">开始实验</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 步骤2: 实验准备与调节 -->
        <div class="step-container" id="step2">
            <div class="step-header">
                <div class="step-number">2</div>
                <div class="step-title">实验准备与调节</div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="step2-progress" style="width: 0%"></div>
            </div>

            <div class="substep-container active" id="step2-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">实验仪调节</div>
                </div>
                <div class="substep-content">
                    <div class="instruction">
                        <h3>实验仪调节步骤：</h3>
                        <ol>
                            <li><strong>水平调节</strong>：使用水平仪调节实验仪底座，确保垂直轨道完全垂直</li>
                            <li><strong>光电门安装</strong>：将光电门安装在轨道底部，确保光路畅通</li>
                            <li><strong>钢球检查</strong>：检查钢球表面是否光滑，无划痕和变形</li>
                            <li><strong>轨道清洁</strong>：清洁轨道内壁，确保钢球下落顺畅</li>
                        </ol>
                    </div>

                    <div class="warning">
                        <strong>调节要点：</strong>
                        <ul>
                            <li>水平调节是实验成功的关键，必须仔细调节</li>
                            <li>光电门位置要固定牢固，避免测量过程中移动</li>
                            <li>确保钢球能够自由下落，不受轨道摩擦影响</li>
                        </ul>
                    </div>

                    <div class="substep-nav">
                        <button onclick="prevStep(2)">返回上一步</button>
                        <button onclick="nextSubStep('step2', 1, 4)">下一步</button>
                    </div>
                </div>
            </div>

            <div class="substep-container" id="step2-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">光电计时器设置</div>
                </div>
                <div class="substep-content">
                    <div class="equipment-setup">
                        <h3>光电计时器操作步骤：</h3>
                        <div class="setup-checklist">
                            <h4>基本设置：</h4>
                            <ul>
                                <li><strong>电源连接</strong>：连接光电计时器电源，打开电源开关</li>
                                <li><strong>模式选择</strong>：选择"计时"模式（通常显示为"T"）</li>
                                <li><strong>精度设置</strong>：设置时间精度为0.001秒（毫秒级）</li>
                                <li><strong>清零操作</strong>：按清零键，确保计时器显示为0.000</li>
                            </ul>
                        </div>

                        <div class="photogate-info">
                            <h4>光电门调节：</h4>
                            <ul>
                                <li><strong>光路检查</strong>：确保发光二极管和光敏二极管之间光路畅通</li>
                                <li><strong>灵敏度调节</strong>：调节光电门灵敏度，确保能检测到钢球通过</li>
                                <li><strong>位置固定</strong>：将光电门固定在轨道底部，确保钢球能完全通过</li>
                                <li><strong>清洁维护</strong>：清洁光电门表面，避免灰尘影响测量</li>
                            </ul>
                        </div>

                        <div class="measurement-tips">
                            <h4>功能测试：</h4>
                            <ul>
                                <li><strong>响应测试</strong>：用手遮挡光电门，观察计时器是否开始计时</li>
                                <li><strong>停止测试</strong>：移开遮挡物，观察计时器是否停止计时</li>
                                <li><strong>显示检查</strong>：检查计时器显示是否清晰，数字是否完整</li>
                                <li><strong>精度验证</strong>：用已知时间间隔测试计时器精度</li>
                            </ul>
                        </div>
                    </div>

                    <div class="highlight">
                        <strong>重要提示：</strong>
                        <p>每次测量前都要清零计时器，确保测量精度。如果计时器显示异常，请检查光电门连接和设置。光电门的清洁和位置固定对测量精度至关重要。</p>
                    </div>

                    <div class="substep-nav">
                        <button onclick="prevSubStep('step2', 2, 4)">上一步</button>
                        <button onclick="nextSubStep('step2', 2, 4)">下一步</button>
                    </div>
                </div>
            </div>

            <div class="substep-container" id="step2-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">高度标定</div>
                </div>
                <div class="substep-content">
                    <div class="instruction">
                        <h3>高度测量与标定：</h3>
                        <ol>
                            <li><strong>基准点确定</strong>：以光电门中心为基准点（h=0）</li>
                            <li><strong>高度测量</strong>：使用米尺测量钢球释放点到光电门的垂直距离</li>
                            <li><strong>多次验证</strong>：重复测量高度，确保测量精度</li>
                            <li><strong>记录数据</strong>：记录每次测量的高度值</li>
                        </ol>
                    </div>

                    <div class="warning">
                        <strong>测量要点：</strong>
                        <ul>
                            <li>高度测量要精确到毫米</li>
                            <li>确保测量的是垂直距离，不是斜距离</li>
                            <li>钢球释放点要固定，避免测量误差</li>
                        </ul>
                    </div>

                    <div class="substep-nav">
                        <button onclick="prevSubStep('step2', 3, 4)">上一步</button>
                        <button onclick="nextSubStep('step2', 3, 4)">下一步</button>
                    </div>
                </div>
            </div>

            <div class="substep-container" id="step2-substep4">
                <div class="substep-header">
                    <div class="substep-number">4</div>
                    <div class="substep-title">预实验测试</div>
                </div>
                <div class="substep-content">
                    <div class="instruction">
                        <h3>预实验测试步骤：</h3>
                        <ol>
                            <li><strong>钢球释放测试</strong>：测试钢球释放机构是否正常工作</li>
                            <li><strong>光电门响应测试</strong>：释放钢球，观察光电门是否正常响应</li>
                            <li><strong>计时器功能测试</strong>：检查计时器是否能正确记录时间</li>
                            <li><strong>数据记录测试</strong>：测试数据记录和读取功能</li>
                        </ol>
                    </div>

                    <div class="success-message" style="display: block;">
                        实验准备完成！所有设备已调节完毕，可以开始正式测量。
                    </div>

                    <div class="substep-nav">
                        <button onclick="prevSubStep('step2', 4, 4)">上一步</button>
                        <button onclick="nextStep(2)">开始测量</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 步骤3: 数据测量 -->
        <div class="step-container" id="step3">
            <div class="step-header">
                <div class="step-number">3</div>
                <div class="step-title">数据测量</div>
            </div>

            <div class="progress-bar">
                <div class="progress-fill" id="step3-progress" style="width: 0%"></div>
            </div>

            <div class="substep-container active" id="step3-substep1">
                <div class="substep-header">
                    <div class="substep-number">1</div>
                    <div class="substep-title">测量方法</div>
                </div>
                <div class="substep-content">
                    <div class="equipment-setup">
                        <h3>测量操作步骤：</h3>
                        <div class="setup-checklist">
                            <h4>测量准备：</h4>
                            <ul>
                                <li><strong>设置高度</strong>：调节钢球释放点高度（建议从0.5m开始）</li>
                                <li><strong>清零计时器</strong>：按清零键，确保计时器显示0.000</li>
                                <li><strong>检查光电门</strong>：确保光电门工作正常，光路畅通</li>
                                <li><strong>钢球准备</strong>：检查钢球表面，确保无划痕和变形</li>
                            </ul>
                        </div>

                        <div class="photogate-info">
                            <h4>测量操作：</h4>
                            <ul>
                                <li><strong>释放钢球</strong>：轻轻释放钢球，避免初速度</li>
                                <li><strong>观察计时</strong>：观察计时器开始计时和停止计时的过程</li>
                                <li><strong>记录时间</strong>：读取并记录计时器显示的时间</li>
                                <li><strong>重复测量</strong>：同一高度重复测量3-5次</li>
                            </ul>
                        </div>

                        <div class="measurement-tips">
                            <h4>测量技巧：</h4>
                            <ul>
                                <li><strong>释放技巧</strong>：使用释放机构或手动释放，确保钢球自由下落</li>
                                <li><strong>读数技巧</strong>：计时器停止后立即读数，避免延迟</li>
                                <li><strong>高度调节</strong>：每次调节高度后要重新测量，确保精度</li>
                                <li><strong>数据记录</strong>：及时记录数据，避免遗忘或混淆</li>
                            </ul>
                        </div>
                    </div>

                    <div class="warning">
                        <strong>测量要点：</strong>
                        <ul>
                            <li>钢球释放时要避免初速度，确保自由下落</li>
                            <li>每次测量前都要清零计时器</li>
                            <li>记录数据要准确，避免读数错误</li>
                            <li>建议测量5个不同高度，每个高度测量3-5次</li>
                            <li>如果测量值异常，要检查设备设置和操作</li>
                        </ul>
                    </div>

                    <div class="substep-nav">
                        <button onclick="prevStep(3)">返回上一步</button>
                        <button onclick="nextSubStep('step3', 1, 3)">下一步</button>
                    </div>
                </div>
            </div>

            <div class="substep-container" id="step3-substep2">
                <div class="substep-header">
                    <div class="substep-number">2</div>
                    <div class="substep-title">数据记录</div>
                </div>
                <div class="substep-content">
                    <div class="data-input-section">
                        <h3>数据记录表</h3>
                        <p>请根据实际测量结果填写下表：</p>
                        <table>
                            <thead>
                                <tr>
                                    <th>测量次数</th>
                                    <th>高度 h (m)</th>
                                    <th>时间 t (s)</th>
                                    <th>重力加速度 g (m/s²)</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td><input type="number" step="0.01" class="height-input" id="h1" placeholder="0.50"></td>
                                    <td><input type="number" step="0.001" class="time-input" id="t1" placeholder="0.320"></td>
                                    <td><span id="g1">-</span></td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td><input type="number" step="0.01" class="height-input" id="h2" placeholder="0.75"></td>
                                    <td><input type="number" step="0.001" class="time-input" id="t2" placeholder="0.392"></td>
                                    <td><span id="g2">-</span></td>
                                </tr>
                                <tr>
                                    <td>3</td>
                                    <td><input type="number" step="0.01" class="height-input" id="h3" placeholder="1.00"></td>
                                    <td><input type="number" step="0.001" class="time-input" id="t3" placeholder="0.452"></td>
                                    <td><span id="g3">-</span></td>
                                </tr>
                                <tr>
                                    <td>4</td>
                                    <td><input type="number" step="0.01" class="height-input" id="h4" placeholder="1.25"></td>
                                    <td><input type="number" step="0.001" class="time-input" id="t4" placeholder="0.505"></td>
                                    <td><span id="g4">-</span></td>
                                </tr>
                                <tr>
                                    <td>5</td>
                                    <td><input type="number" step="0.01" class="height-input" id="h5" placeholder="1.50"></td>
                                    <td><input type="number" step="0.001" class="time-input" id="t5" placeholder="0.553"></td>
                                    <td><span id="g5">-</span></td>
                                </tr>
                            </tbody>
                        </table>
                        <button onclick="calculateGravity()" style="margin-top: 15px;">计算重力加速度</button>
                    </div>

                    <div class="substep-nav">
                        <button onclick="prevSubStep('step3', 2, 3)">上一步</button>
                        <button onclick="nextSubStep('step3', 2, 3)">下一步</button>
                    </div>
                </div>
            </div>

            <div class="substep-container" id="step3-substep3">
                <div class="substep-header">
                    <div class="substep-number">3</div>
                    <div class="substep-title">初步结果</div>
                </div>
                <div class="substep-content">
                    <div id="gravity-results" class="calculation-result" style="display: none;">
                        <h3>计算结果</h3>
                        <div id="gravity-average" class="gravity-result"></div>
                        <div id="gravity-std"></div>
                        <div id="gravity-error"></div>
                    </div>

                    <div class="error-analysis">
                        <h3>误差分析</h3>
                        <ul>
                            <li><strong>系统误差：</strong>空气阻力、仪器精度、光电门响应时间</li>
                            <li><strong>随机误差：</strong>释放时间、测量读数、环境因素</li>
                            <li><strong>减小误差方法：</strong>多次测量、精确计时、环境控制</li>
                        </ul>
                    </div>

                    <div class="substep-nav">
                        <button onclick="prevSubStep('step3', 3, 3)">上一步</button>
                        <button onclick="nextStep(3)">下一步</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 步骤4: 数据分析与图形生成 -->
        <div class="step-container" id="step4">
            <div class="step-header">
                <div class="step-number">4</div>
                <div class="step-title">数据分析与图形生成</div>
            </div>

            <p>根据测量的数据，我们可以绘制高度与时间的关系图，验证自由落体运动规律：</p>

            {{ render_model_selector(available_models, 'model-selector', 'AI模型选择') }}

            <button onclick="generatePlot()">生成图形</button>
            <button onclick="analyzeData()" style="background-color: #27ae60; margin-left: 10px;">AI 分析数据</button>

            <div class="table-container" id="data-table-container" style="margin-top: 20px; display: none;">
                <h3>实验数据汇总</h3>
                <table>
                    <thead>
                        <tr>
                            <th>测量次数</th>
                            <th>高度 h (m)</th>
                            <th>时间 t (s)</th>
                            <th>重力加速度 g (m/s²)</th>
                        </tr>
                    </thead>
                    <tbody id="results-body">
                        <!-- 表格内容将由JavaScript动态生成 -->
                    </tbody>
                </table>
            </div>

            <div id="plot-container">
                <img id="plot-image" style="display: none;">
            </div>

            {{ render_analysis_container('analysis-container', 'analysis-content') }}

            <div class="btn-container">
                <button class="prev-btn" onclick="prevStep(4)">上一步</button>
                <button onclick="finishExperiment()">完成实验</button>
            </div>
        </div>

        <!-- 步骤5: 实验总结 -->
        <div class="step-container" id="step5">
            <div class="step-header">
                <div class="step-number">5</div>
                <div class="step-title">实验总结</div>
            </div>

            <div class="equipment-setup">
                <h3>实验总结要点：</h3>
                <div class="setup-checklist">
                    <h4>实验成果：</h4>
                    <ul>
                        <li><strong>验证了自由落体运动规律</strong>：h = ½gt²</li>
                        <li><strong>测量了当地重力加速度</strong>：通过多次测量获得准确值</li>
                        <li><strong>掌握了光电计时器的使用方法</strong>：提高了实验技能</li>
                        <li><strong>学习了数据处理方法</strong>：包括平均值、标准偏差计算</li>
                    </ul>
                </div>

                <div class="photogate-info">
                    <h4>实验技能提升：</h4>
                    <ul>
                        <li><strong>仪器操作</strong>：光电计时器的设置和使用</li>
                        <li><strong>数据测量</strong>：精确的时间测量和记录</li>
                        <li><strong>误差分析</strong>：识别和减小实验误差</li>
                        <li><strong>数据处理</strong>：科学的数据处理方法</li>
                    </ul>
                </div>

                <div class="measurement-tips">
                    <h4>注意事项回顾：</h4>
                    <ul>
                        <li><strong>水平调节</strong>：确保实验仪完全垂直</li>
                        <li><strong>光电门维护</strong>：保持清洁和正确位置</li>
                        <li><strong>钢球释放</strong>：避免初速度影响</li>
                        <li><strong>数据记录</strong>：准确记录和及时整理</li>
                    </ul>
                </div>
            </div>

            <div class="success-message" style="display: block;">
                恭喜！您已完成自由落体实验的所有步骤。请提交实验数据以完成实验。
            </div>

            <div class="btn-container">
                <button class="prev-btn" onclick="prevStep(5)">上一步</button>
                <button onclick="finishExperiment()">提交实验</button>
            </div>
        </div>

        <div class="footer">
            © 2024 | 物理实验基础课程平台 | 网页设计：朱瑞华
        </div>
    </div>

    <!-- 提交模态框 -->
    {{ render_modal('submission-modal', '提交实验结果', '
        <p>请输入您的学号和姓名以完成实验提交：</p>
        <div class="input-group">
            <label for="student-id">学号：</label>
            <input type="text" id="student-id" placeholder="请输入学号">
        </div>
        <div class="input-group">
            <label for="student-name">姓名：</label>
            <input type="text" id="student-name" placeholder="请输入姓名">
        </div>
        <div id="submission-status" style="display: none;"></div>
    ', [
        {'text': '提交', 'onclick': 'submitExperiment()'},
        {'text': '取消', 'onclick': 'closeSubmitModal()', 'class': 'prev-btn'}
    ]) }}

    <!-- 引入必要的库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/showdown/2.1.0/showdown.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.8/dist/katex.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/showdown-katex@0.8.0/dist/showdown-katex.min.js"></script>

    <!-- 引入通用实验脚本 -->
    <script src="{{ url_for('static', filename='js/experiment.js') }}"></script>

    <!-- 自由落体实验特有脚本 -->
    <script>
        // 计算重力加速度
        function calculateGravity() {
            const heights = [];
            const times = [];
            const gravityValues = [];

            // 收集数据
            for (let i = 1; i <= 5; i++) {
                const h = parseFloat(document.getElementById(`h${i}`).value);
                const t = parseFloat(document.getElementById(`t${i}`).value);
                
                if (!isNaN(h) && !isNaN(t) && t > 0) {
                    heights.push(h);
                    times.push(t);
                    const g = (2 * h) / (t * t);
                    gravityValues.push(g);
                    
                    // 显示单个重力加速度值
                    document.getElementById(`g${i}`).textContent = g.toFixed(2);
                } else {
                    document.getElementById(`g${i}`).textContent = '-';
                }
            }

            // 计算平均值和标准偏差
            if (gravityValues.length > 0) {
                const avgG = gravityValues.reduce((a, b) => a + b, 0) / gravityValues.length;
                const variance = gravityValues.reduce((sum, g) => sum + Math.pow(g - avgG, 2), 0) / gravityValues.length;
                const stdG = Math.sqrt(variance);

                // 显示结果
                const resultsDiv = document.getElementById('gravity-results');
                document.getElementById('gravity-average').innerHTML = `平均重力加速度：${avgG.toFixed(2)} m/s²`;
                document.getElementById('gravity-std').innerHTML = `标准偏差：${stdG.toFixed(2)} m/s²`;
                document.getElementById('gravity-error').innerHTML = `相对误差：${Math.abs(avgG - 9.8) / 9.8 * 100}%`;
                resultsDiv.style.display = 'block';
            }
        }

        // 生成图形
        function generatePlot() {
            const heights = [];
            const times = [];
            const gravityValues = [];

            // 收集有效数据
            for (let i = 1; i <= 5; i++) {
                const h = parseFloat(document.getElementById(`h${i}`).value);
                const t = parseFloat(document.getElementById(`t${i}`).value);
                
                if (!isNaN(h) && !isNaN(t) && t > 0) {
                    heights.push(h);
                    times.push(t);
                    const g = (2 * h) / (t * t);
                    gravityValues.push(g);
                }
            }

            if (heights.length === 0) {
                alert('请先输入有效的测量数据！');
                return;
            }

            // 创建数据表格
            const resultsBody = document.getElementById('results-body');
            resultsBody.innerHTML = '';

            for (let i = 0; i < heights.length; i++) {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${i + 1}</td>
                    <td>${heights[i].toFixed(2)}</td>
                    <td>${times[i].toFixed(3)}</td>
                    <td>${gravityValues[i].toFixed(2)}</td>
                `;
                resultsBody.appendChild(row);
            }

            // 显示数据表格
            document.getElementById('data-table-container').style.display = 'block';

            // 发送数据到服务器生成图形
            fetch('/free_fall/plot', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    heights: heights,
                    times: times,
                    gravity_values: gravityValues
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }
                const plotImage = document.getElementById('plot-image');
                plotImage.src = 'data:image/png;base64,' + data.plot_url;
                plotImage.style.display = 'block';

                // 滚动到图形位置
                document.getElementById('plot-container').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            })
            .catch(error => {
                console.error('Error:', error);
                alert('生成图形时发生错误：' + error.message);
            });
        }

        // AI分析数据
        function analyzeData() {
            const heights = [];
            const times = [];
            const gravityValues = [];

            // 收集有效数据
            for (let i = 1; i <= 5; i++) {
                const h = parseFloat(document.getElementById(`h${i}`).value);
                const t = parseFloat(document.getElementById(`t${i}`).value);
                
                if (!isNaN(h) && !isNaN(t) && t > 0) {
                    heights.push(h);
                    times.push(t);
                    const g = (2 * h) / (t * t);
                    gravityValues.push(g);
                }
            }

            if (heights.length === 0) {
                alert('请先输入有效的测量数据！');
                return;
            }

            // 获取选择的模型ID
            const selectedModelId = document.getElementById('model-selector').value;

            // 显示分析容器和加载状态
            const analysisContainer = document.getElementById('analysis-container');
            const analysisContent = document.getElementById('analysis-content');
            analysisContainer.style.display = 'block';
            analysisContent.innerHTML = '<p>AI正在分析数据，请稍候...</p>';

            // 滚动到分析容器
            analysisContainer.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // 使用通用的AI分析函数
            analyzeWithAI('/free_fall/analyze', {
                heights: heights,
                times: times,
                gravity_values: gravityValues
            }, selectedModelId,
                // 成功回调
                (renderedHTML) => {
                    displayAnalysisResult('analysis-container', 'analysis-content', renderedHTML);
                },
                // 错误回调
                (error) => {
                    displayAnalysisResult('analysis-container', 'analysis-content', 
                        `<p style="color: #e74c3c;">连接服务器时发生错误，请重试！</p>`);
                }
            );
        }

        // 完成实验
        function finishExperiment() {
            // 显示提交对话框
            document.getElementById('submission-modal').style.display = 'flex';
        }

        // 关闭提交对话框
        function closeSubmitModal() {
            document.getElementById('submission-modal').style.display = 'none';
            document.getElementById('submission-status').style.display = 'none';
            document.getElementById('submission-status').innerHTML = '';
        }

        // 提交实验数据
        function submitExperiment() {
            const studentId = document.getElementById('student-id').value.trim();
            const studentName = document.getElementById('student-name').value.trim();

            // 验证输入
            if (!studentId || !studentName) {
                document.getElementById('submission-status').style.display = 'block';
                document.getElementById('submission-status').style.backgroundColor = '#f8d7da';
                document.getElementById('submission-status').innerHTML = '错误：学号和姓名不能为空！';
                return;
            }

            // 获取实验数据
            const heights = [];
            const times = [];
            const gravityValues = [];

            for (let i = 1; i <= 5; i++) {
                const h = parseFloat(document.getElementById(`h${i}`).value);
                const t = parseFloat(document.getElementById(`t${i}`).value);
                
                if (!isNaN(h) && !isNaN(t) && t > 0) {
                    heights.push(h);
                    times.push(t);
                    const g = (2 * h) / (t * t);
                    gravityValues.push(g);
                }
            }

            // 获取已生成的图片数据
            const plotImage = document.getElementById('plot-image');
            let plotData = null;
            if (plotImage && plotImage.src && plotImage.style.display !== 'none') {
                if (plotImage.src.startsWith('data:image/png;base64,')) {
                    plotData = plotImage.src.split(',')[1];
                }
            }

            // 获取AI分析结果
            const analysisContent = document.getElementById('analysis-content');
            let analysisResult = null;
            if (analysisContent && analysisContent.innerHTML &&
                document.getElementById('analysis-container').style.display !== 'none') {
                analysisResult = analysisContent.innerText || analysisContent.textContent;
            }

            // 准备提交数据
            const submissionData = {
                student_id: studentId,
                student_name: studentName,
                heights: heights,
                times: times,
                gravity_values: gravityValues,
                plot_data: plotData,
                analysis_result: analysisResult
            };

            // 显示提交中状态
            document.getElementById('submission-status').style.display = 'block';
            document.getElementById('submission-status').style.backgroundColor = '#e8f4fc';
            document.getElementById('submission-status').innerHTML = '正在提交实验数据，请稍候...';

            // 使用通用提交处理函数
            handleExperimentSubmission('free_fall', submissionData,
                // 成功回调
                (result) => {
                    document.getElementById('submission-status').style.backgroundColor = '#d4edda';
                    document.getElementById('submission-status').innerHTML = `恭喜！实验数据已成功提交。记录ID: ${result.record_id}`;

                    // 3秒后关闭对话框
                    setTimeout(() => {
                        closeSubmitModal();
                        alert('实验已成功完成并提交！');
                    }, 3000);
                },
                // 错误回调
                (error) => {
                    document.getElementById('submission-status').style.backgroundColor = '#f8d7da';
                    document.getElementById('submission-status').innerHTML = '错误：' + error;
                }
            );
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 为输入框添加实时计算功能
            const inputs = document.querySelectorAll('.height-input, .time-input');
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    // 延迟计算，避免频繁触发
                    clearTimeout(window.gravityCalculationTimeout);
                    window.gravityCalculationTimeout = setTimeout(calculateGravity, 500);
                });
            });

            // 初始化KaTeX渲染
            if (typeof katex !== 'undefined') {
                const katexElements = document.querySelectorAll('.katex');
                katexElements.forEach(element => {
                    try {
                        katex.render(element.textContent, element);
                    } catch (e) {
                        console.warn('KaTeX渲染失败:', e);
                    }
                });
            }
        });

        // 步骤导航函数
        function nextSubStep(step, substep, total) {
            document.getElementById(`${step}-substep${substep}`).classList.remove('active');
            document.getElementById(`${step}-substep${substep + 1}`).classList.add('active');
            document.getElementById(`${step}-progress`).style.width = `${(substep / total) * 100}%`;
        }

        function prevSubStep(step, substep, total) {
            document.getElementById(`${step}-substep${substep}`).classList.remove('active');
            document.getElementById(`${step}-substep${substep - 1}`).classList.add('active');
            document.getElementById(`${step}-progress`).style.width = `${((substep - 2) / total) * 100}%`;
        }

        function nextStep(currentStep) {
            document.getElementById(`step${currentStep}`).classList.remove('active');
            document.getElementById(`step${currentStep + 1}`).classList.add('active');
        }

        function prevStep(currentStep) {
            document.getElementById(`step${currentStep}`).classList.remove('active');
            document.getElementById(`step${currentStep - 1}`).classList.add('active');
        }
    </script>
</body>
</html>
