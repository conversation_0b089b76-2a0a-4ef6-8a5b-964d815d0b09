<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>科学计算器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 10px;
            margin: 0;
        }

        .calculator-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            padding: 15px;
            width: 100%;
            max-width: 350px;
            backdrop-filter: blur(10px);
        }

        .calculator-header {
            text-align: center;
            margin-bottom: 15px;
        }

        .calculator-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 3px;
        }

        .calculator-subtitle {
            font-size: 12px;
            color: #666;
        }

        .display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            text-align: right;
            font-size: 24px;
            font-weight: 500;
            color: #333;
            min-height: 60px;
            display: flex;
            align-items: center;
            justify-content: flex-end;
            word-wrap: break-word;
            word-break: break-all;
        }

        .buttons-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
        }

        .btn {
            padding: 12px 6px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn:active {
            transform: scale(0.95);
        }

        .btn-number {
            background: linear-gradient(145deg, #ffffff, #e6e6e6);
            color: #333;
            box-shadow: 5px 5px 10px #d1d1d1, -5px -5px 10px #ffffff;
        }

        .btn-number:hover {
            background: linear-gradient(145deg, #f0f0f0, #e0e0e0);
        }

        .btn-operator {
            background: linear-gradient(145deg, #ff9500, #ff8000);
            color: white;
            box-shadow: 5px 5px 10px #d17a00, -5px -5px 10px #ffb000;
        }

        .btn-operator:hover {
            background: linear-gradient(145deg, #ff8000, #ff7000);
        }

        .btn-function {
            background: linear-gradient(145deg, #6c757d, #5a6268);
            color: white;
            box-shadow: 5px 5px 10px #4a4a4a, -5px -5px 10px #8e8e8e;
        }

        .btn-function:hover {
            background: linear-gradient(145deg, #5a6268, #495057);
        }

        .btn-equals {
            background: linear-gradient(145deg, #28a745, #20c997);
            color: white;
            box-shadow: 5px 5px 10px #1e7e34, -5px -5px 10px #32d4a0;
        }

        .btn-equals:hover {
            background: linear-gradient(145deg, #20c997, #17a2b8);
        }

        .btn-clear {
            background: linear-gradient(145deg, #dc3545, #c82333);
            color: white;
            box-shadow: 5px 5px 10px #a71e2a, -5px -5px 10px #e74c3c;
        }

        .btn-clear:hover {
            background: linear-gradient(145deg, #c82333, #bd2130);
        }

        .btn-delete {
            background: linear-gradient(145deg, #fd7e14, #e55a00);
            color: white;
            box-shadow: 5px 5px 10px #c45a00, -5px -5px 10px #ffa000;
        }

        .btn-delete:hover {
            background: linear-gradient(145deg, #e55a00, #d63384);
        }

        .btn-scientific {
            background: linear-gradient(145deg, #6f42c1, #5a2d91);
            color: white;
            box-shadow: 5px 5px 10px #4a1f6b, -5px -5px 10px #9465d1;
        }

        .btn-scientific:hover {
            background: linear-gradient(145deg, #5a2d91, #4c1d95);
        }

        .btn-wide {
            grid-column: span 2;
        }

        .btn-tall {
            grid-row: span 2;
        }

        .history {
            margin-top: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            max-height: 120px;
            overflow-y: auto;
        }

        .history h4 {
            margin-bottom: 8px;
            color: #333;
            font-size: 14px;
        }

        .history-item {
            padding: 3px 0;
            border-bottom: 1px solid #e9ecef;
            font-size: 12px;
            color: #666;
        }

        .history-item:last-child {
            border-bottom: none;
        }

        @media (max-width: 480px) {
            .calculator-container {
                padding: 10px;
                margin: 5px;
                max-width: 320px;
            }
            
            .btn {
                padding: 10px 4px;
                font-size: 14px;
            }
            
            .display {
                font-size: 20px;
                padding: 10px;
                min-height: 50px;
            }
            
            .calculator-title {
                font-size: 18px;
            }
            
            .calculator-subtitle {
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <div class="calculator-container">
        <div class="calculator-header">
            <div class="calculator-title">科学计算器</div>
            <div class="calculator-subtitle">支持基础运算和科学函数</div>
        </div>

        <div class="display" id="display">0</div>

        <div class="buttons-grid">
            <!-- 第一行 -->
            <button class="btn btn-clear" onclick="clearDisplay()">C</button>
            <button class="btn btn-delete" onclick="deleteLastChar()">⌫</button>
            <button class="btn btn-function" onclick="appendToDisplay('(')">(</button>
            <button class="btn btn-function" onclick="appendToDisplay(')')">)</button>

            <!-- 第二行 -->
            <button class="btn btn-scientific" onclick="appendToDisplay('sin(')">sin</button>
            <button class="btn btn-scientific" onclick="appendToDisplay('cos(')">cos</button>
            <button class="btn btn-scientific" onclick="appendToDisplay('tan(')">tan</button>
            <button class="btn btn-operator" onclick="appendToDisplay('/')">/</button>

            <!-- 第三行 -->
            <button class="btn btn-scientific" onclick="appendToDisplay('log(')">log</button>
            <button class="btn btn-scientific" onclick="appendToDisplay('ln(')">ln</button>
            <button class="btn btn-scientific" onclick="appendToDisplay('sqrt(')">√</button>
            <button class="btn btn-operator" onclick="appendToDisplay('*')">×</button>

            <!-- 第四行 -->
            <button class="btn btn-scientific" onclick="appendToDisplay('^')">x^y</button>
            <button class="btn btn-scientific" onclick="appendToDisplay('pi')">π</button>
            <button class="btn btn-scientific" onclick="appendToDisplay('e')">e</button>
            <button class="btn btn-operator" onclick="appendToDisplay('-')">-</button>

            <!-- 第五行 -->
            <button class="btn btn-number" onclick="appendToDisplay('7')">7</button>
            <button class="btn btn-number" onclick="appendToDisplay('8')">8</button>
            <button class="btn btn-number" onclick="appendToDisplay('9')">9</button>
            <button class="btn btn-operator btn-tall" onclick="appendToDisplay('+')">+</button>

            <!-- 第六行 -->
            <button class="btn btn-number" onclick="appendToDisplay('4')">4</button>
            <button class="btn btn-number" onclick="appendToDisplay('5')">5</button>
            <button class="btn btn-number" onclick="appendToDisplay('6')">6</button>

            <!-- 第七行 -->
            <button class="btn btn-number" onclick="appendToDisplay('1')">1</button>
            <button class="btn btn-number" onclick="appendToDisplay('2')">2</button>
            <button class="btn btn-number" onclick="appendToDisplay('3')">3</button>

            <!-- 第八行 -->
            <button class="btn btn-number btn-wide" onclick="appendToDisplay('0')">0</button>
            <button class="btn btn-number" onclick="appendToDisplay('.')">.</button>
            <button class="btn btn-equals btn-tall" onclick="calculate()">=</button>
        </div>

        <div class="history" id="history">
            <h4>计算历史</h4>
            <div id="history-content"></div>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjs/11.8.0/math.min.js"></script>
    <script>
        let displayValue = '0';
        let history = [];

        function updateDisplay() {
            document.getElementById('display').textContent = displayValue;
        }

        function appendToDisplay(value) {
            if (displayValue === '0' && value !== '.') {
                displayValue = value;
            } else {
                displayValue += value;
            }
            updateDisplay();
        }

        function clearDisplay() {
            displayValue = '0';
            updateDisplay();
        }

        function deleteLastChar() {
            if (displayValue.length === 1) {
                displayValue = '0';
            } else {
                displayValue = displayValue.slice(0, -1);
            }
            updateDisplay();
        }

        function calculate() {
            try {
                // 替换一些符号以适应 math.js
                let expression = displayValue
                    .replace(/\^/g, '^')
                    .replace(/pi/g, 'pi')
                    .replace(/e(?![0-9])/g, 'e')
                    .replace(/sin\(/g, 'sin(')
                    .replace(/cos\(/g, 'cos(')
                    .replace(/tan\(/g, 'tan(')
                    .replace(/log\(/g, 'log10(')
                    .replace(/ln\(/g, 'log(')
                    .replace(/sqrt\(/g, 'sqrt(');

                const result = math.evaluate(expression);
                const resultStr = result.toString();
                
                // 添加到历史记录
                addToHistory(displayValue + ' = ' + resultStr);
                
                displayValue = resultStr;
                updateDisplay();
            } catch (error) {
                displayValue = 'Error';
                updateDisplay();
                setTimeout(clearDisplay, 1500);
            }
        }

        function addToHistory(calculation) {
            history.unshift(calculation);
            if (history.length > 10) {
                history.pop();
            }
            updateHistory();
        }

        function updateHistory() {
            const historyContent = document.getElementById('history-content');
            historyContent.innerHTML = history.map(item => 
                `<div class="history-item">${item}</div>`
            ).join('');
        }

        // 键盘支持
        document.addEventListener('keydown', function(event) {
            const key = event.key;
            
            if (key >= '0' && key <= '9' || key === '.') {
                appendToDisplay(key);
            } else if (key === '+' || key === '-' || key === '*' || key === '/') {
                appendToDisplay(key);
            } else if (key === 'Enter' || key === '=') {
                calculate();
            } else if (key === 'Backspace') {
                deleteLastChar();
            } else if (key === 'Escape') {
                clearDisplay();
            } else if (key === '(' || key === ')') {
                appendToDisplay(key);
            }
        });

        // 初始化显示
        updateDisplay();
    </script>
</body>
</html> 