// ./app/static/js/experiment.js
// 统一的实验JavaScript文件

// 当前步骤
let currentStep = 1;

// 切换到下一步
function nextStep(step) {
    const currentStepElement = document.getElementById(`step${step}`);
    const nextStepElement = document.getElementById(`step${step + 1}`);

    if (currentStepElement && nextStepElement) {
        currentStepElement.classList.remove('active');
        nextStepElement.classList.add('active');
        currentStep = step + 1;

        // 滚动到新步骤
        nextStepElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 返回上一步
function prevStep(step) {
    const currentStepElement = document.getElementById(`step${step}`);
    const prevStepElement = document.getElementById(`step${step - 1}`);

    if (currentStepElement && prevStepElement) {
        currentStepElement.classList.remove('active');
        prevStepElement.classList.add('active');
        currentStep = step - 1;

        // 滚动到新步骤
        prevStepElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 切换到下一个子步骤
function nextSubStep(stepId, currentSubStep, totalSubSteps) {
    const currentSubStepElement = document.getElementById(`${stepId}-substep${currentSubStep}`);
    const nextSubStepElement = document.getElementById(`${stepId}-substep${currentSubStep + 1}`);

    if (currentSubStepElement && nextSubStepElement) {
        // 隐藏当前子步骤
        currentSubStepElement.classList.remove('active');

        // 显示下一个子步骤
        nextSubStepElement.classList.add('active');

        // 更新进度条
        const progressElement = document.getElementById(`${stepId}-progress`);
        if (progressElement) {
            const progressPercent = (currentSubStep / totalSubSteps) * 100;
            progressElement.style.width = `${progressPercent}%`;
        }

        // 滚动到新子步骤
        nextSubStepElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 返回上一个子步骤
function prevSubStep(stepId, currentSubStep, totalSubSteps) {
    const currentSubStepElement = document.getElementById(`${stepId}-substep${currentSubStep}`);
    const prevSubStepElement = document.getElementById(`${stepId}-substep${currentSubStep - 1}`);

    if (currentSubStepElement && prevSubStepElement) {
        // 隐藏当前子步骤
        currentSubStepElement.classList.remove('active');

        // 显示上一个子步骤
        prevSubStepElement.classList.add('active');

        // 更新进度条
        const progressElement = document.getElementById(`${stepId}-progress`);
        if (progressElement) {
            const progressPercent = ((currentSubStep - 2) / totalSubSteps) * 100;
            progressElement.style.width = `${Math.max(0, progressPercent)}%`;
        }

        // 滚动到新子步骤
        prevSubStepElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 跳转到上一个主步骤的最后一个子步骤
function goToPrevStepLastSubstep(currentStepNumber) {
    const prevStepNumber = currentStepNumber - 1;
    if (prevStepNumber < 1) return;

    // 隐藏当前步骤
    const currentStepElement = document.getElementById(`step${currentStepNumber}`);
    if (currentStepElement) {
        currentStepElement.classList.remove('active');
    }

    // 显示上一个步骤
    const prevStepElement = document.getElementById(`step${prevStepNumber}`);
    if (prevStepElement) {
        prevStepElement.classList.add('active');
        let substeps = prevStepElement.querySelectorAll('.substep-container');

        if (substeps.length > 0) {
            // 如果有子步骤，则跳转到最后一个子步骤
            substeps.forEach(sub => sub.classList.remove('active'));
            const lastSubstep = substeps[substeps.length - 1];
            lastSubstep.classList.add('active');

            // 更新进度条
            const progressElement = document.getElementById(`step${prevStepNumber}-progress`);
            if (progressElement) {
                const progressPercent = ((substeps.length - 1) / substeps.length) * 100;
                progressElement.style.width = `${progressPercent}%`;
            }

            // 滚动到视图
            lastSubstep.scrollIntoView({ behavior: 'smooth', block: 'start' });
        } else {
            // 如果没有子步骤，直接滚动到步骤标题
            prevStepElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
}

// Markdown和数学公式渲染函数
function renderMarkdownAndMath(markdownText) {
    try {
        // 检查必要的库是否已加载
        if (typeof showdown === 'undefined' || typeof showdownKatex === 'undefined') {
            console.warn('Showdown or showdown-katex not loaded, returning plain text');
            return markdownText;
        }

        // 配置 showdown-katex 扩展
        const katexOptions = {
            displayMode: false,
            throwOnError: false,
            errorColor: '#ff0000',
            delimiters: [
                { left: "$$", right: "$$", display: true },
                { left: "$", right: "$", display: false },
                { left: "\\(", right: "\\)", display: false },
                { left: "\\[", right: "\\]", display: true }
            ]
        };

        // 初始化 Showdown 转换器，并添加 showdown-katex 扩展
        const converter = new showdown.Converter({
            extensions: [showdownKatex(katexOptions)],
            tables: true,
            strikethrough: true,
            tasklists: true
        });

        // 一次性将 Markdown 和数学公式转换为 HTML
        const html = converter.makeHtml(markdownText);

        return html;
    } catch (error) {
        console.error('Error rendering markdown:', error);
        return markdownText;
    }
}

// 通用的AI分析函数
function analyzeWithAI(endpoint, data, selectedModelId = null, onSuccess = null, onError = null) {
    const requestData = { ...data };
    if (selectedModelId) {
        requestData.model_id = parseInt(selectedModelId);
    }

    return fetch(endpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('服务器响应错误');
        }
        return response.text();
    })
    .then(markdownText => {
        const renderedHTML = renderMarkdownAndMath(markdownText);
        if (onSuccess) onSuccess(renderedHTML);
        return renderedHTML;
    })
    .catch(error => {
        console.error('AI分析错误:', error);
        if (onError) onError(error.message);
        throw error;
    });
}

// 通用的加载动画函数
function showLoading(message = '正在处理...') {
    // 移除已存在的加载动画
    hideLoading();

    const loadingHTML = `
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-content">
                <div class="loading-spinner"></div>
                <p>${message}</p>
            </div>
        </div>
    `;

    const loadingDiv = document.createElement('div');
    loadingDiv.innerHTML = loadingHTML;
    document.body.appendChild(loadingDiv);
}

function hideLoading() {
    const loading = document.getElementById('loadingOverlay');
    if (loading) {
        loading.remove();
    }
}

// 通用的模态框函数
function showModal(title, content, buttons = []) {
    const modalHTML = `
        <div class="modal-overlay" id="generalModal">
            <div class="modal-content">
                <h3>${title}</h3>
                <div class="modal-body">${content}</div>
                <div class="modal-buttons">
                    ${buttons.map(btn => `<button onclick="${btn.action}" ${btn.class ? `class="${btn.class}"` : ''}>${btn.text}</button>`).join('')}
                </div>
            </div>
        </div>
    `;

    const modalDiv = document.createElement('div');
    modalDiv.innerHTML = modalHTML;
    document.body.appendChild(modalDiv);
}

function closeModal() {
    const modal = document.getElementById('generalModal');
    if (modal) {
        modal.remove();
    }
}

// 通用的数据提交函数
function _submitExperimentData(experimentType, data) {
    const submissionData = {
        experiment_type: experimentType,
        ...data
    };

    return fetch('/submit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(submissionData)
    });
}

// 实验数据验证函数
function validateExperimentData(experimentType, data) {
    switch (experimentType) {
        case 'zhiliu_circuit':
            return validateZhiliuCircuitData(data);
        case 'oscilloscope':
            return validateOscilloscopeData(data);
        default:
            return { valid: false, message: '未知的实验类型' };
    }
}

function validateZhiliuCircuitData(data) {
    if (!data.k1_current || !data.k01_current) {
        return { valid: false, message: '制流电路实验数据不完整' };
    }

    if (!Array.isArray(data.k1_current) || !Array.isArray(data.k01_current)) {
        return { valid: false, message: '电流数据格式不正确' };
    }

    if (data.k1_current.length !== 11 || data.k01_current.length !== 11) {
        return { valid: false, message: '电流数据点数不正确，应为11个数据点' };
    }

    return { valid: true };
}

function validateOscilloscopeData(data) {
    if (!data.measurement_data) {
        return { valid: false, message: '示波器测量数据不存在' };
    }

    const { generator, oscilloscope } = data.measurement_data;

    if (!generator || !oscilloscope) {
        return { valid: false, message: '示波器测量数据不完整' };
    }

    const requiredGeneratorFields = ['voltage', 'frequency'];
    const requiredOscilloscopeFields = ['measuredVoltage', 'measuredFrequency'];

    for (const field of requiredGeneratorFields) {
        if (typeof generator[field] !== 'number' || isNaN(generator[field])) {
            return { valid: false, message: `函数发生器${field}数据无效` };
        }
    }

    for (const field of requiredOscilloscopeFields) {
        if (typeof oscilloscope[field] !== 'number' || isNaN(oscilloscope[field])) {
            return { valid: false, message: `示波器${field}数据无效` };
        }
    }

    return { valid: true };
}

// 通用的提交处理函数
function handleExperimentSubmission(experimentType, data, onSuccess, onError) {
    // 验证数据
    const validation = validateExperimentData(experimentType, data);
    if (!validation.valid) {
        if (onError) onError(validation.message);
        return;
    }

    // 显示加载状态
    showLoading('正在提交实验数据...');

    // 提交数据
    _submitExperimentData(experimentType, data)
        .then(response => {
            if (!response.ok) {
                throw new Error('服务器响应错误');
            }
            return response.json();
        })
        .then(result => {
            hideLoading();
            if (result.success) {
                if (onSuccess) onSuccess(result);
            } else {
                if (onError) onError(result.message);
            }
        })
        .catch(error => {
            hideLoading();
            if (onError) onError(error.message);
        });
}

// 通用的分析结果显示函数
function displayAnalysisResult(containerId, contentId, analysisHTML, show = true) {
    const container = document.getElementById(containerId);
    const content = document.getElementById(contentId);

    if (container && content) {
        content.innerHTML = analysisHTML;
        if (show) {
            container.style.display = 'block';
            container.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }
    }
}

// 通用的全屏表格函数
function openFullscreenTable(tableId) {
    const table = document.getElementById(tableId);
    if (table) {
        table.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function closeFullscreenTable(tableId) {
    const table = document.getElementById(tableId);
    if (table) {
        table.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// 通用计算器切换功能
let calculatorStates = {}; // 跟踪每个计算器的显示状态

function toggleCalculator(containerId) {
    const calculatorContainer = document.getElementById(containerId);
    const toggleBtn = document.querySelector(`#${containerId}`).previousElementSibling.querySelector('.calculator-toggle-btn');

    // 初始化状态
    if (calculatorStates[containerId] === undefined) {
        calculatorStates[containerId] = false;
    }

    if (calculatorStates[containerId]) {
        // 隐藏计算器
        calculatorContainer.style.display = 'none';
        toggleBtn.innerHTML = '🧮 使用计算器辅助计算';
        calculatorStates[containerId] = false;
    } else {
        // 显示计算器
        calculatorContainer.style.display = 'block';
        toggleBtn.innerHTML = '🧮 关闭计算器';
        calculatorStates[containerId] = true;

        // 如果iframe不存在，则创建
        if (!calculatorContainer.querySelector('iframe')) {
            const iframe = document.createElement('iframe');
            iframe.src = '/calculator';
            iframe.style.width = '100%';
            iframe.style.height = '500px';
            iframe.style.border = 'none';
            iframe.style.borderRadius = '8px';
            iframe.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
            calculatorContainer.appendChild(iframe);
        }
    }
}

// 通用标准差计算函数
function calculateStdDev(data, mean) {
    if (!data || data.length < 2) return 0;
    const n = data.length;
    const variance = data.reduce((sum, x) => sum + Math.pow(x - mean, 2), 0) / (n - 1);
    return Math.sqrt(variance);
}

// 通用实验数据汇总表生成函数
function generateExperimentSummaryTable(tableBodyId, dataConfig) {
    const tbody = document.getElementById(tableBodyId);
    if (!tbody) return;

    tbody.innerHTML = '';

    dataConfig.forEach(config => {
        const { label, measurements, mean, uA, uB, uC, unit } = config;
        let rowHTML = `<tr><td>${label} (${unit})</td>`;

        // 添加测量数据列
        for (let i = 0; i < 5; i++) {
            rowHTML += `<td>${measurements && measurements[i] !== undefined ? measurements[i] : ''}</td>`;
        }

        // 添加统计数据列
        rowHTML += `<td>${mean !== undefined ? mean.toFixed(3) : ''}</td>`;
        rowHTML += `<td>${uA !== undefined ? uA.toFixed(5) : ''}</td>`;
        rowHTML += `<td>${uB !== undefined ? uB.toFixed(5) : ''}</td>`;
        rowHTML += `<td>${uC !== undefined ? uC.toFixed(5) : ''}</td>`;
        rowHTML += '</tr>';

        tbody.innerHTML += rowHTML;
    });
}

// 通用实验提交模态框函数
function showExperimentSubmitModal(modalId, experimentType) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
    }
}

function closeExperimentSubmitModal(modalId, statusId) {
    const modal = document.getElementById(modalId);
    const status = document.getElementById(statusId);

    if (modal) {
        modal.style.display = 'none';
    }
    if (status) {
        status.style.display = 'none';
        status.innerHTML = '';
    }
}

// 通用实验数据提交函数
function submitExperimentData(experimentType, studentIdInputId, studentNameInputId, statusId, dataCollector) {
    const studentId = document.getElementById(studentIdInputId).value.trim();
    const studentName = document.getElementById(studentNameInputId).value.trim();
    const statusDiv = document.getElementById(statusId);

    if (!studentId || !studentName) {
        statusDiv.style.display = 'block';
        statusDiv.style.backgroundColor = '#f8d7da';
        statusDiv.innerHTML = '错误：学号和姓名不能为空！';
        return;
    }

    // 收集实验数据
    const experimentData = dataCollector ? dataCollector() : {};

    // 显示提交状态
    statusDiv.style.display = 'block';
    statusDiv.style.backgroundColor = '#e8f4fc';
    statusDiv.innerHTML = '正在提交实验数据，请稍候...';

    // 微小长度测量实验提交
    if (window.location.pathname.includes('micro_length_lever')) {
        const nInputs = document.getElementsByClassName('n-value');
        const x0Inputs = document.getElementsByClassName('x0-value');
        const x1Inputs = document.getElementsByClassName('x1-value');
        const b = parseFloat(document.querySelector('.param-b').value);
        const D = parseFloat(document.querySelector('.param-D').value);
        let n_list = [], s_list = [], d_list = [], valid = true;
        for (let i = 0; i < nInputs.length; i++) {
            const n = parseFloat(nInputs[i].value);
            const x0 = parseFloat(x0Inputs[i].value);
            const x1 = parseFloat(x1Inputs[i].value);
            if (isNaN(n) || isNaN(x0) || isNaN(x1) || n <= 0) {
                valid = false;
                break;
            }
            n_list.push(n);
            s_list.push(x1 - x0);
            d_list.push(s_list[s_list.length-1] * b / (2 * n * D));
        }
        if (!valid || isNaN(b) || isNaN(D)) {
            document.getElementById('submission-status').style.display = 'block';
            document.getElementById('submission-status').style.backgroundColor = '#f8d7da';
            document.getElementById('submission-status').innerHTML = '错误：请填写完整且有效的数据！';
            return;
        }
        // 获取已生成的图片数据
        const plotImage = document.getElementById('plot-image');
        let plotImageData = null;
        if (plotImage && plotImage.src && plotImage.style.display !== 'none') {
            if (plotImage.src.startsWith('data:image/png;base64,')) {
                plotImageData = plotImage.src.split(',')[1];
            }
        }
        // 获取AI分析结果
        const analysisContent = document.getElementById('analysis-content');
        let analysisResult = null;
        if (analysisContent && analysisContent.innerHTML &&
            document.getElementById('analysis-container').style.display !== 'none') {
            analysisResult = analysisContent.innerText || analysisContent.textContent;
        }
        // 准备提交数据
        const plotData = {
            n_list: n_list,
            s_list: s_list,
            b: b,
            D: D
        };
        const submissionData = {
            student_id: studentId,
            student_name: studentName,
            n_list: n_list,
            s_list: s_list,
            b: b,
            D: D,
            d_list: d_list,
            plot_data: plotImageData,
            analysis_result: analysisResult
        };
        document.getElementById('submission-status').style.display = 'block';
        document.getElementById('submission-status').style.backgroundColor = '#e8f4fc';
        document.getElementById('submission-status').innerHTML = '正在提交实验数据，请稍候...';
        handleExperimentSubmission('micro_length_lever', submissionData,
            (result) => {
                document.getElementById('submission-status').style.backgroundColor = '#d4edda';
                document.getElementById('submission-status').innerHTML = `恭喜！实验数据已成功提交。记录ID: ${result.record_id}`;
                setTimeout(() => {
                    closeSubmitModal();
                    alert('实验已成功完成并提交！');
                }, 3000);
            },
            (error) => {
                document.getElementById('submission-status').style.backgroundColor = '#f8d7da';
                document.getElementById('submission-status').innerHTML = '错误：' + error;
            }
        );
        return;
    }

    // 模拟提交过程
    setTimeout(() => {
        statusDiv.style.backgroundColor = '#d4edda';
        statusDiv.innerHTML = '实验数据已成功提交！';
        setTimeout(() => {
            const modalId = statusDiv.closest('.modal').id;
            closeExperimentSubmitModal(modalId, statusId);
            alert('实验已成功完成并提交！');
        }, 2000);
    }, 1500);
}

// 通用数据验证函数
function validateMeasurementData(className, dataName, nextButtonId, displayElementId = null, expectedCount = 5) {
    const inputs = document.getElementsByClassName(className);
    let measurementData = [];
    let allFilled = true;

    for (let input of inputs) {
        if (!input.value || isNaN(parseFloat(input.value))) {
            allFilled = false;
            break;
        }
        measurementData.push(parseFloat(input.value));
    }

    if (allFilled && measurementData.length === expectedCount) {
        alert(`${dataName}数据验证通过！`);
        if (nextButtonId) {
            const nextBtn = document.getElementById(nextButtonId);
            if (nextBtn) nextBtn.disabled = false;
        }

        if (displayElementId) {
            const displayElement = document.getElementById(displayElementId);
            if (displayElement) {
                displayElement.innerHTML =
                    `${dataName}数据：${measurementData.map((d, i) => `${dataName.charAt(0)}${i + 1} = ${d} mm`).join(', ')}`;
            }
        }

        return measurementData;
    } else {
        alert(`请填写所有${dataName}数据，且数据必须为有效数字！`);
        if (nextButtonId) {
            const nextBtn = document.getElementById(nextButtonId);
            if (nextBtn) nextBtn.disabled = true;
        }
        return null;
    }
}

// 通用计算验证函数
function validateCalculationResult(studentValue, correctValue, tolerance, resultElementId, nextButtonId, successMessage, errorMessage) {
    if (isNaN(studentValue)) {
        alert('请输入有效的数值！');
        return false;
    }

    const isCorrect = Math.abs(studentValue - correctValue) < tolerance;
    const resultDiv = document.getElementById(resultElementId);

    if (resultDiv) {
        resultDiv.style.display = 'block';
        if (isCorrect) {
            resultDiv.innerHTML = `✅ ${successMessage}`;
            resultDiv.style.backgroundColor = '#d4edda';
            resultDiv.style.color = '#155724';
        } else {
            resultDiv.innerHTML = `❌ ${errorMessage}`;
            resultDiv.style.backgroundColor = '#f8d7da';
            resultDiv.style.color = '#721c24';
        }
    }

    if (nextButtonId) {
        const nextBtn = document.getElementById(nextButtonId);
        if (nextBtn) nextBtn.disabled = !isCorrect;
    }

    return isCorrect;
}

// 微小长度测量实验通用函数
function generateMicroLengthResult() {
    const nInputs = document.getElementsByClassName('n-value');
    const x0Inputs = document.getElementsByClassName('x0-value');
    const x1Inputs = document.getElementsByClassName('x1-value');
    const sInputs = document.getElementsByClassName('s-value');
    const b = parseFloat(document.querySelector('.param-b').value);
    const D = parseFloat(document.querySelector('.param-D').value);
    if (isNaN(b) || isNaN(D)) {
        alert('请填写b和D参数！');
        return;
    }
    let n_list = [], s_list = [], d_list = [], valid = true;
    for (let i = 0; i < nInputs.length; i++) {
        const n = parseFloat(nInputs[i].value);
        const x0 = parseFloat(x0Inputs[i].value);
        const x1 = parseFloat(x1Inputs[i].value);
        if (isNaN(n) || isNaN(x0) || isNaN(x1) || n <= 0) {
            valid = false;
            break;
        }
        n_list.push(n);
        s_list.push(x1 - x0);
        d_list.push(s_list[s_list.length-1] * b / (2 * n * D));
        if (sInputs[i]) sInputs[i].value = (x1 - x0).toFixed(2);
    }
    if (!valid) {
        alert('请填写完整且有效的数据！');
        return;
    }
    // 生成结果表格
    const resultTable = document.getElementById('result-table-container');
    const resultBody = document.getElementById('result-body');
    resultBody.innerHTML = '';
    for (let i = 0; i < n_list.length; i++) {
        const row = document.createElement('tr');
        row.innerHTML = `<td>${i+1}</td><td>${n_list[i]}</td><td>${s_list[i].toFixed(2)}</td><td>${d_list[i].toFixed(4)}</td>`;
        resultBody.appendChild(row);
    }
    resultTable.style.display = 'block';
    // 计算平均值和标准差
    const mean = d_list.reduce((a, b) => a + b, 0) / d_list.length;
    const std = Math.sqrt(d_list.map(x => Math.pow(x - mean, 2)).reduce((a, b) => a + b, 0) / d_list.length);
    document.getElementById('summary-result').innerHTML = `平均纸厚：${mean.toFixed(4)} mm，标准差：${std.toFixed(4)} mm`;
}

// 微小长度测量实验AI分析函数
function analyzeMicroLengthData() {
    const nInputs = document.getElementsByClassName('n-value');
    const x0Inputs = document.getElementsByClassName('x0-value');
    const x1Inputs = document.getElementsByClassName('x1-value');
    const L1 = parseFloat(document.querySelector('.param-L1').value);
    const L2 = parseFloat(document.querySelector('.param-L2').value);
    const D = parseFloat(document.querySelector('.param-D').value);
    let n_list = [], s_list = [], valid = true;
    for (let i = 0; i < nInputs.length; i++) {
        const n = parseFloat(nInputs[i].value);
        const x0 = parseFloat(x0Inputs[i].value);
        const x1 = parseFloat(x1Inputs[i].value);
        if (isNaN(n) || isNaN(x0) || isNaN(x1) || n <= 0) {
            valid = false;
            break;
        }
        n_list.push(n);
        s_list.push(x1 - x0);
    }
    if (!valid || isNaN(L1) || isNaN(L2)) {
        alert('请填写完整且有效的数据！');
        return;
    }
    const selectedModelId = document.getElementById('model-selector').value;
    const analysisContainer = document.getElementById('analysis-container');
    const analysisContent = document.getElementById('analysis-content');
    analysisContainer.style.display = 'block';
    analysisContent.innerHTML = '<p>AI正在分析数据，请稍候...</p>';
    analysisContainer.scrollIntoView({behavior:'smooth',block:'start'});
    fetch('/micro_length_lever/analyze', {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify({n_list, s_list, L1, L2, D, model_id: selectedModelId})
    })
    .then(res=>res.text())
    .then(txt=>{
        analysisContent.innerHTML = txt.replace(/\n/g,'<br>');
    })
    .catch(err=>{
        analysisContent.innerHTML = `<span style='color:red'>AI分析失败：${err.message}</span>`;
    });
}
